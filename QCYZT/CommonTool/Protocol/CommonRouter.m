//
//  CommonRouter.m
//  QCYZT
//
//  Created by zeng on 2022/11/4.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "CommonRouter.h"
#import "LZRouter.h"
#import "StockViewController.h"
#import "FMChooseBrokerViewController.h"
#import "StockTradeDetailViewController.h"
#import "FMHomePageVC.h"
#import "FMBigCastViewController.h"
#import "FMBigCastHomePageViewController.h"
#import "FMNoteDetailViewController.h"
#import "CourseDetailViewController.h"
#import "CourseAlbumListViewController.h"
#import "CourseAlbumDetailViewController.h"
#import "FMAskCodeViewController.h"
#import "FMAskCodeDetailViewController.h"
#import "AskQuestionViewController.h"
#import "AskCodeAnswerViewController.h"
#import "FMMarketForcastViewController.h"
#import "FMTrainingCampDetailVC.h"
#import "FMTrainingCampListVC.h"
#import "HttpRequestTool+Live.h"
#import "FMDakaLiveDetailVC.h"
#import "FMMessageViewController.h"
#import "FMCouponViewController.h"
#import "ConsumeHistoryViewController.h"
#import "FMMyCollectionViewController.h"
#import "FMShareModel.h"
#import "FMShareHelper.h"
#import "InviteRegisterViewController.h"
#import "FMAllDakaListVC.h"
#import "NSString+URLManage.h"
#import "FMAddWechatNumView.h"
#import "FMInfomationDetailVC.h"
#import "FMInfomationSubVC.h"
#import "FMMakeElectronicInvoiceViewController.h"
#import "FMElectronicInvoiceRecordViewController.h"
#import "FMTopicDetailViewController.h"
#import "FMComplaintViewController.h"
#import "FMHomeDailyReadVC.h"
#import "FMMemberCenterOldViewController.h"
#import "YTGOtherWebVC.h"
#import "FMRiskEvaluationReminderVC.h"
#import "FMPrivateLetterViewController.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMPrivateLetterNoRightViewController.h"
#import "FMBigcastPrivateLetterViewController.h"
#import "YTGNormalWebVC.h"
#import "FMMemberCenterProductViewController.h"
#import "FMBigCastOperationRankViewController.h"
#import "FMMainSearchViewController.h"
#import "FMInvestorInfoViewController.h"
#import "FMTaskConfigModel.h"
#import "FMTrainingCampChapterDetailVC.h"
#import "HttpRequestTool+Daka.h"
#import "FMVIPXYItemTabVC.h"
#import "FMPDFReaderViewController.h"
#import "FMVIPXLZFDetailVC.h"
#import "FMMainCourseViewController.h"
#import "FMCourseTypeVC.h"
#import "FMCourseCategoryVC.h"
#import "FMPurchasedServiceVC.h"
#import "FMWinnerMainViewController.h"
#import "FMIndexMallViewController.h"
#import "FMIPOCalendarViewController.h"
#import "FMIPOCalendarApplyDetailViewController.h"
#import "FMWeChatLoginViewController.h"
#import "FMLoginViewController.h"
#import "FMMemberCenterVIPJCViewController.h"
#import "FMWKWebViewController.h"
#import "FMStockStrategyDetailViewController.h"
#import "FMStockStrategyMineVC.h"
#import "FMStockLimitUpFocusViewController.h"
#import "FMF10AnnouncementDetailVC.h"
#import "FMF10ResearchReportVC.h"
#import "FMInstitutionHoldingVC.h"
#import "FMPurchasedServiceVC.h"
#import "FMUnlockPositionViewController.h"
#import "FMStockRZRQViewController.h"
#import "FMStockBigDealController.h"
#import "FMGSYJViewController.h"
#import "FMGSYJReprotViewController.h"
#import "FMRealtimeDragonTigerViewController.h"
#import "MemberCenterVisitManager.h"

@implementation CommonRouter

+ (void)load {
#pragma mark - 一级页面
    // 登录、注册
    [LZRouter registerURLPattern:Login_URL toHandler:^(NSDictionary *routerParameters) {
        [FMUserDefault clearLocalUserData];
        
        if ([FMHelper getIAPPayStatus]) {
            FMLoginViewController *vc = [FMLoginViewController new];
            FMNavigationController *nav = [[FMNavigationController alloc] initWithRootViewController:vc];
            nav.modalPresentationStyle = UIModalPresentationFullScreen;
            [[FMHelper getCurrentVC].navigationController presentViewController:nav animated:YES completion:nil];
            return;
        }
        
        for (UIViewController *vc in [FMHelper getCurrentVC].navigationController.viewControllers) {
            if ([vc isKindOfClass:[FMWeChatLoginViewController class]]) {
                return;
            }
        }
        
        FMWeChatLoginViewController *loginVC = [[FMWeChatLoginViewController alloc] init];
        FMNavigationController *nav = [[FMNavigationController alloc] initWithRootViewController:loginVC];
        nav.modalPresentationStyle = UIModalPresentationFullScreen;
        [[FMHelper getCurrentVC].navigationController presentViewController:nav animated:YES completion:nil];
    }];
    
    [LZRouter registerURLPattern:@"qcyzt://register" toHandler:^(NSDictionary *routerParameters) {
        for (UIViewController *vc in [FMHelper getCurrentVC].navigationController.viewControllers) {
            if ([vc isKindOfClass:[FMWeChatLoginViewController class]]) {
                return;
            }
        }
        
        FMWeChatLoginViewController *loginVC = [[FMWeChatLoginViewController alloc] init];
        FMNavigationController *nav = [[FMNavigationController alloc] initWithRootViewController:loginVC];
        nav.modalPresentationStyle = UIModalPresentationFullScreen;
        [[FMHelper getCurrentVC].navigationController presentViewController:nav animated:YES completion:nil];
    }];
    
    // 首页
    [LZRouter registerURLPattern:@"qcyzt://home" toHandler:^(NSDictionary *routerParameters) {
        [self gotoFirstLevelVCWithIndex:0 innerIndex:-1 params:routerParameters];
    }];
    
    // 笔记列表、笔记详情
    [LZRouter registerURLPattern:@"qcyzt://note" toHandler:^(NSDictionary *routerParameters) {
        NSString *noteId = [routerParameters objectForKey:@"id"];
        if (noteId.length == 0) {
            [self gotoFirstLevelVCWithIndex:0 innerIndex:2 params:routerParameters];
        } else {
            FMNoteDetailViewController *noteDetailVC = [[FMNoteDetailViewController alloc] init];
            noteDetailVC.noteId = noteId;
            [[FMHelper getCurrentVC].navigationController pushViewController:noteDetailVC animated:YES];
        }
    }];
    
    // 直播
    [LZRouter registerURLPattern:@"qcyzt://dakalive" toHandler:^(NSDictionary *routerParameters) {
        NSString *roomid = [routerParameters objectForKey:@"roomid"];
        NSString *password = [routerParameters objectForKey:@"password"];
        if (roomid.length > 0) {
            [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                FMDakaLiveDetailVC *vc = [[FMDakaLiveDetailVC alloc] initWithRoomId:roomid passWord:password];
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            }];
        } else {
            [self gotoFirstLevelVCWithIndex:1 innerIndex:4 params:routerParameters];
        }
    }];
    [LZRouter registerURLPattern:@"qcyzt://live" toHandler:^(NSDictionary *routerParameters) {
        NSString *roomid = [routerParameters objectForKey:@"roomid"];
        if (roomid.length > 0) {
            [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                FMDakaLiveDetailVC *vc = [[FMDakaLiveDetailVC alloc] initWithRoomId:roomid];
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            }];
        } else {
            [self gotoFirstLevelVCWithIndex:1 innerIndex:4 params:routerParameters];
        }
    }];
    
    // 自选股
    [LZRouter registerURLPattern:@"qcyzt://optionlstocks" toHandler:^(NSDictionary *routerParameters) {
        [self gotoFirstLevelVCWithIndex:2 innerIndex:0 params:routerParameters];

        UINavigationController *nvc = [FMHelper getCurrentVC].navigationController;
        StockViewController *vc = (StockViewController *)[nvc.childViewControllers firstObject];
        vc.selectedType = SelectedTypeSelfStock;
    }];
    
    // 大盘指数
    [LZRouter registerURLPattern:@"qcyzt://marketindex" toHandler:^(NSDictionary *routerParameters) {
        [self gotoFirstLevelVCWithIndex:2 innerIndex:1 params:routerParameters];

        UINavigationController *nvc = [FMHelper getCurrentVC].navigationController;
        StockViewController *vc = (StockViewController *)[nvc.childViewControllers firstObject];
        vc.selectedType = SelectedTypeMarket;
    }];
    
    // 解盘协议
    [LZRouter registerURLPattern:@"qcyzt://watchlive" toHandler:^(NSDictionary *routerParameters) {
        [HttpRequestTool getLiveListWithPageNo:1 pageSize:2 signId:@"" bigNameId:nil start:^{
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD dismiss];
                NSArray *arr = dic[@"data"];
                NSInteger tag = 0;
                for (NSDictionary *dict in arr) {
                    if ([[NSString stringWithFormat:@"%@", dict[@"status"]] integerValue] == 1) {
                        tag++;
                    }
                }
                if (tag == 2 || tag == 0) {
                    [self gotoFirstLevelVCWithIndex:1 innerIndex:4 params:routerParameters];
                } else if (tag == 1) {
                    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                        NSDictionary *dict = arr.firstObject;
                        FMDakaLiveDetailVC *vc = [[FMDakaLiveDetailVC alloc] initWithRoomId:[NSString stringWithFormat:@"%@", dict[@"id"]]];
                        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
                    }];
                }
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }];
    
    // 资讯
    [LZRouter registerURLPattern:@"qcyzt://information" toHandler:^(NSDictionary *routerParameters) {
        [self gotoFirstLevelVCWithIndex:3 innerIndex:0 params:routerParameters];
    }];
    
    // 个人中心
    [LZRouter registerURLPattern:@"qcyzt://usercenter" toHandler:^(NSDictionary *routerParameters) {
        [self gotoFirstLevelVCWithIndex:4 innerIndex:0 params:routerParameters];
    }];

#pragma mark - 非一级页面
#pragma mark -- 首页相关
    // 所有投顾
    [LZRouter registerURLPattern:@"qcyzt://allbigcast" toHandler:^(NSDictionary *routerParameters) {
        FMAllDakaListVC *vc = [[FMAllDakaListVC alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 投顾列表、投顾详情
    [LZRouter registerURLPattern:@"qcyzt://bigname" toHandler:^(NSDictionary *routerParameters) {
        NSString *bigCastId = [routerParameters objectForKey:@"id"];
        NSInteger firstLevelIndex = [[routerParameters objectForKey:@"firstLevel"] integerValue];
        NSInteger secondLevelIndex = [[routerParameters objectForKey:@"secondLevel"] integerValue];
        if (bigCastId.length == 0) {
            FMBigCastViewController *vc = [[FMBigCastViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        } else {
            if (bigCastId.length) {
                FMBigCastHomePageViewController *bigCastDetailVC = [[FMBigCastHomePageViewController alloc] init];
                bigCastDetailVC.userId = bigCastId;
                bigCastDetailVC.firstLevelIndex = firstLevelIndex;
                bigCastDetailVC.secondLevelIndex = secondLevelIndex;
                [[FMHelper getCurrentVC].navigationController pushViewController:bigCastDetailVC animated:YES];
            }
        } 
    }];
        
    // 投顾神操作
    [LZRouter registerURLPattern:@"qcyzt://bigcastoperationrank" toHandler:^(NSDictionary *routerParameters) {
        FMBigCastOperationRankViewController *vc = [[FMBigCastOperationRankViewController alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 关注投顾
    [LZRouter registerURLPattern:@"qcyzt://bigcastfollow" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            NSString *bigCastId = [routerParameters objectForKey:@"id"];
            [HttpRequestTool focusDakaWithUserId:bigCastId roomId:@"" start:^{
            } failure:^{
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"关注成功"];
                    [[FMUserDataSyncManager sharedManager] noticeDaka:bigCastId];
                    [[NSNotificationCenter defaultCenter] postNotificationName:kFocusNumChanged object:@{@"dakaId":bigCastId,@"focus":@"1 "}];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        }];
    }];
    
    // 话题详情
    [LZRouter registerURLPattern:@"qcyzt://topic" toHandler:^(NSDictionary *routerParameters) {
        NSString *topicId = [routerParameters objectForKey:@"id"];
        if (topicId.length) {
            UIViewController *currentVC = [FMHelper getCurrentVC];
            if ([currentVC isKindOfClass:[FMTopicDetailViewController class]]) {
                FMTopicDetailViewController *tmpVC = (FMTopicDetailViewController *)currentVC;
                if ([tmpVC.topicId isEqualToString:topicId]) {
                    return;
                }
            }
            
            FMTopicDetailViewController *vc = [[FMTopicDetailViewController alloc] init];
            vc.topicId = topicId;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        } else {
            
        }
    }];
    
    // 课程列表、课程详情
    [LZRouter registerURLPattern:@"qcyzt://course" toHandler:^(NSDictionary *routerParameters) {
        NSString *courseId = [routerParameters objectForKey:@"id"];
        NSString *index = [routerParameters objectForKey:@"index"];
        if (courseId.length == 0) {
            FMMainCourseViewController *courseVC = [[FMMainCourseViewController alloc] init];
//            courseVC.index = index.integerValue;
            [[FMHelper getCurrentVC].navigationController pushViewController:courseVC animated:YES];
        } else {
            CourseDetailViewController *courseDetailVC = [[CourseDetailViewController alloc] init];
            courseDetailVC.courseId = courseId;
            [[FMHelper getCurrentVC].navigationController pushViewController:courseDetailVC animated:YES];
        }
    }];
    
    // 标签课程
    [LZRouter registerURLPattern:@"qcyzt://coursetype" toHandler:^(NSDictionary *routerParameters) {
        NSString *courseId = [routerParameters objectForKey:@"id"];
        NSString *title = [routerParameters objectForKey:@"title"];
        FMCourseTypeVC *vc = [[FMCourseTypeVC alloc] init];
        vc.courseSignIds = courseId;
        vc.pageTitle = [title URLDecodedString];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 课程分类
    [LZRouter registerURLPattern:@"qcyzt://coursecategory" toHandler:^(NSDictionary *routerParameters) {
        FMCourseCategoryVC *vc = [[FMCourseCategoryVC alloc] init];
        vc.pageType = CourseCategoryTypeAll;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 系列课程列表、详情
    [LZRouter registerURLPattern:@"qcyzt://seriescourse" toHandler:^(NSDictionary *routerParameters) {
        NSString *albumId = [routerParameters objectForKey:@"id"];
        if (albumId.length == 0) { // 基本不存在此跳转
            CourseAlbumListViewController *albumVC = [[CourseAlbumListViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:albumVC animated:YES];
        } else {
            CourseAlbumDetailViewController *albumDetailVC = [[CourseAlbumDetailViewController alloc] init];
            albumDetailVC.albumId = albumId;
            [[FMHelper getCurrentVC].navigationController pushViewController:albumDetailVC animated:YES];
        }
    }];
    
    // 系列章节详情
    [LZRouter registerURLPattern:@"qcyzt://coursechapters" toHandler:^(NSDictionary *routerParameters) {
        NSString *albumChapterId = [routerParameters objectForKey:@"id"];
        CourseDetailViewController *courseDetailVC = [[CourseDetailViewController alloc] init];
        courseDetailVC.courseId = albumChapterId;
        courseDetailVC.isChapterDetail = YES;
        [[FMHelper getCurrentVC].navigationController pushViewController:courseDetailVC animated:YES];
    }];
    
    // 问股列表、问股详情
    [LZRouter registerURLPattern:@"qcyzt://questionstock" toHandler:^(NSDictionary *routerParameters) {
        NSString *askId = [routerParameters objectForKey:@"id"];
        if (askId.length == 0) {
            FMAskCodeViewController *vc = [[FMAskCodeViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        } else {
            FMAskCodeDetailViewController *askCodeDetailVC = [[FMAskCodeDetailViewController alloc] init];
            askCodeDetailVC.questionId = askId;
            [[FMHelper getCurrentVC].navigationController pushViewController:askCodeDetailVC animated:YES];
        }
    }];
    
    // 问股
    [LZRouter registerURLPattern:@"qcyzt://ask" toHandler:^(NSDictionary *routerParameters) {
        NSString *code = routerParameters[@"code"];
        NSString *setCode = routerParameters[@"setcode"];
        UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:[FMUPDataTool jointWithSetCode:[setCode integerValue] code:code]];
        AskQuestionViewController *questionVC = [[AskQuestionViewController alloc] init];
        if (info) {
            questionVC.relationStock = info;
        }
        [[FMHelper getCurrentVC].navigationController pushViewController:questionVC animated:YES];
    }];
    
    // 回答问股
    [LZRouter registerURLPattern:@"qcyzt://answerstock" toHandler:^(NSDictionary *routerParameters) {
        NSString *askId = [routerParameters objectForKey:@"id"];
        if (askId.length) {
            AskCodeAnswerViewController *askCodeVC = [[AskCodeAnswerViewController alloc] init];
            askCodeVC.questionId = askId;
            [[FMHelper getCurrentVC].navigationController pushViewController:askCodeVC animated:YES];
        }
    }];
    
    // 训练营列表、详情
    [LZRouter registerURLPattern:@"qcyzt://retailcampsdetail" toHandler:^(NSDictionary *routerParameters) {
        NSString *bookId = [routerParameters objectForKey:@"id"];
        if (bookId.length) {
            FMTrainingCampDetailVC *trainingCampVC = [[FMTrainingCampDetailVC alloc] initWithBookId:bookId];
            [[FMHelper getCurrentVC].navigationController pushViewController:trainingCampVC animated:YES];
        } else {
            FMTrainingCampListVC *trainingCampVC = [[FMTrainingCampListVC alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:trainingCampVC animated:YES];
        }
    }];
    
    // 训练营章节详情
    [LZRouter registerURLPattern:@"qcyzt://retailcampschapterdetail" toHandler:^(NSDictionary *routerParameters) {
        NSString *bookId = [routerParameters objectForKey:@"campId"];
        NSString *chapterId = [routerParameters objectForKey:@"chapterId"];
        if (bookId.length && chapterId.length) {
            FMTrainingCampChapterDetailVC *vc = [[FMTrainingCampChapterDetailVC alloc] init];
            vc.campId = bookId;
            vc.chapterId = chapterId;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }];
    
    // 训练营评论
    [LZRouter registerURLPattern:@"qcyzt://retailcampsdetailcomment" toHandler:^(NSDictionary *routerParameters) {
        NSString *bookId = [routerParameters objectForKey:@"id"];
        if (bookId.length) {
            FMTrainingCampDetailVC *trainingCampVC = [[FMTrainingCampDetailVC alloc] initWithBookId:bookId];
            trainingCampVC.currentIndex = 2;
            [[FMHelper getCurrentVC].navigationController pushViewController:trainingCampVC animated:YES];
        }
    }];
    
    // 大盘预测
    [LZRouter registerURLPattern:@"qcyzt://stockpredict" toHandler:^(NSDictionary *routerParameters) {
        FMMarketForcastViewController *forecastVC = [[FMMarketForcastViewController alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:forecastVC animated:YES];
    }];
    
    // 每日必看
    [LZRouter registerURLPattern:@"qcyzt://notedaily" toHandler:^(NSDictionary *routerParameters) {
        FMHomeDailyReadVC *vc = [[FMHomeDailyReadVC alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
#pragma mark -- 直播相关

    
#pragma mark -- 行情相关
    // 股票详情
    [LZRouter registerURLPattern:@"qcyzt://productdetails" toHandler:^(NSDictionary *routerParameters) {
        // 参数 code=sh000001
        NSString *codeStr = [routerParameters objectForKey:@"code"];
        NSInteger category = [[routerParameters objectForKey:@"category"] integerValue];
        if (category >= 6 && category <= 8) {
            UPRouterNavigate([@"upchina://market/stock" up_buildURLWithQueryParams:@{
                @"setcode" : @"0",
                @"code" : codeStr,
                @"category" : @(FMMarketStockCategory_BLOCK) // 后期添加，标识板块用
            }]);
        } else {
            if (codeStr.length > 2) {
                UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:codeStr];
                if (matchInfo) {
                    [UPRouterUtil goMarketStock:matchInfo.setCode code:matchInfo.code];
                }
            }
        }
    }];
    
    // 股票详情页下内部跳转
    [LZRouter registerURLPattern:@"qcyzt://stockdetail" toHandler:^(NSDictionary *routerParameters) {
        NSInteger index = [[routerParameters objectForKey:@"tabIndex"] integerValue];
        NSInteger subIndex = [[routerParameters objectForKey:@"subTabIndex"] integerValue];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"stockDetailBottomSwitchToIndex" object:nil userInfo:@{@"index" : @(index), @"subIndex" : @(subIndex)}];
    }];
    
    // 资金流向
    [LZRouter registerURLPattern:@"qcyzt://moneyflow" toHandler:^(NSDictionary *routerParameters) {
        [UPRouterUtil goMarketFundFlow:nil];
    }];
    
    // 交易
    [LZRouter registerURLPattern:@"qcyzt://trade" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            // 参数 type=buy(类型) id=dgzq(券商) code=股票code
            NSString *type = [routerParameters objectForKey:@"type"];
            NSString *brokerId = [routerParameters objectForKey:@"id"];
            NSString *code = [routerParameters objectForKey:@"code"];
            if (!type.length && !brokerId.length) {
                FMChooseBrokerViewController *vc = [[FMChooseBrokerViewController alloc] init];
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            } else if (brokerId.length) {
                if ([type isEqualToString:@"open"]) {
                    if ([brokerId isEqualToString:@"dycy"]) {
//                        RXWebSubViewController *rxWeb = [[RXWebSubViewController alloc] initWithUrl:[NSURL URLWithString:YCKaiHuUrl]];
//                        FMNavigationController *khNav = [[FMNavigationController alloc]initWithRootViewController:rxWeb];
//                        khNav.modalPresentationStyle = UIModalPresentationFullScreen;
//                        [[FMHelper getCurrentVC].navigationController presentViewController:khNav animated:YES completion:nil];
                    }
                } else {
                    StockTradeType tradeType = StockTradeTypeTradeHome;
                    if ([type isEqualToString:@"main"]) {
                    }  else if ([type isEqualToString:@"buy"]) {
                        tradeType = StockTradeTypeTradeBuy;
                    }  else if ([type isEqualToString:@"sell"]) {
                        tradeType = StockTradeTypeTradeSell;
                    } else if ([type isEqualToString:@"hold"]) {
                        tradeType = StockTradeTypeTradeHold;
                    }
                    
                    StockTradeDetailViewController *vc = [StockTradeDetailViewController shareInstance];
                    vc.brokerId = brokerId;
                    vc.tradeType = tradeType;
                    vc.stockCode = code;
                    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
                }
            }
        }];
    }];
    
    // 龙虎榜
    [LZRouter registerURLPattern:@"qcyzt://lhrank" toHandler:^(NSDictionary *routerParameters) {
        NSInteger index = [[routerParameters objectForKey:@"index"] integerValue];
        NSInteger subIndex = [[routerParameters objectForKey:@"subIndex"] integerValue];
        FMWinnerMainViewController *vc = [FMWinnerMainViewController new];
        vc.index = index;
        vc.subIndex = subIndex;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 实时龙虎榜
    [LZRouter registerURLPattern:@"qcyzt://lhrank2" toHandler:^(NSDictionary *routerParameters) {
        FMRealtimeDragonTigerViewController *vc = [FMRealtimeDragonTigerViewController new];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 指标商城
    [LZRouter registerURLPattern:@"qcyzt://stockindex" toHandler:^(NSDictionary *routerParameters) {
        FMIndexMallViewController *vc = [FMIndexMallViewController new];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 新股日历
    [LZRouter registerURLPattern:@"qcyzt://ipocalendar" toHandler:^(NSDictionary *routerParameters) {
        FMIPOCalendarViewController *vc = [FMIPOCalendarViewController new];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 申购详情
    [LZRouter registerURLPattern:@"qcyzt://stockapplydetail" toHandler:^(NSDictionary *routerParameters) {
        FMIPOCalendarApplyDetailViewController *vc = [FMIPOCalendarApplyDetailViewController new];
        NSString *code = [routerParameters objectForKey:@"stockCode"];
        if (code.length) {
            vc.stockCode = code;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }];
    
    // 指标策略
    [LZRouter registerURLPattern:@"qcyzt://indexstrategy" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMStockStrategyDetailViewController *vc = [FMStockStrategyDetailViewController new];
            NSString *templateId = [routerParameters objectForKey:@"templateId"];
            if (templateId.length) {
                vc.templateId = [templateId integerValue];
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            }
        }];
    }];

    // 涨停聚焦
    [LZRouter registerURLPattern:@"qcyzt://ztjj2" toHandler:^(NSDictionary *routerParameters) {
        FMStockLimitUpFocusViewController *vc = [FMStockLimitUpFocusViewController new];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 公告
    [LZRouter registerURLPattern:@"qcyzt://stockannouncement" toHandler:^(NSDictionary *routerParameters) {
        NSString *announcementId = [routerParameters objectForKey:@"id"];
        NSString *announcementLink = [routerParameters objectForKey:@"link"];
        if (announcementLink.length) {
            FMWKWebViewController *webVC = [[FMWKWebViewController alloc] init];
            webVC.urlStr = announcementLink;
            webVC.title = @"公告详情";
            [[FMHelper getCurrentVC].navigationController pushViewController:webVC animated:YES];
        } else if (announcementId.length) {
            FMF10AnnouncementDetailVC *vc = [FMF10AnnouncementDetailVC new];
            vc.announcementId = announcementId;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }];
    
    // 研报
    [LZRouter registerURLPattern:@"qcyzt://stockreport" toHandler:^(NSDictionary *routerParameters) {
        NSString *reportId = [routerParameters objectForKey:@"id"];
        if (reportId.length) {
            FMF10ResearchReportVC *vc = [FMF10ResearchReportVC new];
            vc.reportId = reportId;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }];
    
    // 机构持仓
    [LZRouter registerURLPattern:@"qcyzt://stockinstitutionholds" toHandler:^(NSDictionary *routerParameters) {
        NSString *code = [routerParameters objectForKey:@"code"];
        NSString *name = [[routerParameters objectForKey:@"name"] URLDecodedString];
        if (code.length) {
            FMInstitutionHoldingVC *vc = [FMInstitutionHoldingVC new];
            [vc configStockName:name stockCode:code];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }];
    
    // 解套宝
    [LZRouter registerURLPattern:@"qcyzt://jtb" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMUnlockPositionViewController *vc = [FMUnlockPositionViewController new];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 大宗交易
    [LZRouter registerURLPattern:@"qcyzt://blocktrade" toHandler:^(NSDictionary *routerParameters) {
        FMStockBigDealController *vc = [FMStockBigDealController new];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 融资融券
    [LZRouter registerURLPattern:@"qcyzt://margintrade" toHandler:^(NSDictionary *routerParameters) {
        FMStockRZRQViewController *vc = [FMStockRZRQViewController new];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
#pragma mark -- 快讯相关
    // 资讯详情
    [LZRouter registerURLPattern:@"qcyzt://slicedetail" toHandler:^(NSDictionary *routerParameters) {
        [self gotoFirstLevelVCWithIndex:3 innerIndex:-1 params:routerParameters];
    }];

    // 要闻(一图看懂)、财联社
    [LZRouter registerURLPattern:@"qcyzt://importantnews" toHandler:^(NSDictionary *routerParameters) {
        NSString *contentId = [routerParameters objectForKey:@"id"];
        NSInteger type = [[routerParameters objectForKey:@"type"] integerValue];
        if (contentId.length > 0) {
            FMInfomationDetailVC *vc = [[FMInfomationDetailVC alloc] init];
            vc.detailType = type;
            vc.articleId =  [contentId integerValue];;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        } else {
            FMInfomationSubVC *vc = [[FMInfomationSubVC alloc] initWithPageType:1];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }];
    
#pragma mark -- 个人中心相关
    // VIP中心
    [LZRouter registerURLPattern:@"qcyzt://vipcenter" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMMemberCenterOldViewController *vc = [[FMMemberCenterOldViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 投顾会员
    [LZRouter registerURLPattern:@"qcyzt://vipproduct" toHandler:^(NSDictionary *routerParameters) {
        NSString *bigCastId = [routerParameters objectForKey:@"id"];
        if (bigCastId.length) {
            FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
            vc.bigcastId = bigCastId;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }];

    
    [LZRouter registerURLPattern:@"qcyzt://vipproduct2" toHandler:^(NSDictionary *routerParameters) {
        NSString *type = [routerParameters objectForKey:@"type"];
        if (!type.length) {
            return;
        }
        
        JCStockPoolType poolType = [[routerParameters objectForKey:@"poolType"] integerValue];
        JCVIPType vipType = JCVIPTypeNone;
        if ([type isEqualToString:@"jcql"]) {
            vipType = JCVIPTypeJCQL;
            poolType += 1;
        } else if ([type isEqualToString:@"jcdj"]) {
            vipType = JCVIPTypeJCDJ;
            poolType += 11;
        }
        
        FMMemberCenterVIPJCViewController *vc = [[FMMemberCenterVIPJCViewController alloc] initWithType:vipType poolType:poolType];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 股市赢家
    [LZRouter registerURLPattern:@"qcyzt://vipproduct3" toHandler:^(NSDictionary *routerParameters) {
        FMGSYJViewController *vc = [FMGSYJViewController new];
        vc.serverType = JCVIPTypeGSYJ;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 股市赢家定制
    [LZRouter registerURLPattern:@"qcyzt://vipproduct4" toHandler:^(NSDictionary *routerParameters) {
        FMGSYJViewController *vc = [FMGSYJViewController new];
        vc.serverType = JCVIPTypeGSYJDZ;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];

    // 首席课堂
    [LZRouter registerURLPattern:@"qcyzt://vipvideo" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            NSInteger serverType = [[routerParameters objectForKey:@"serverType"] integerValue];
            NSInteger contentId = [[routerParameters objectForKey:@"tid"] integerValue];
            
            FMVIPXLZFDetailVC *vc = [[FMVIPXLZFDetailVC alloc] initWithType:2 contentType:0];
            vc.contentId = contentId;
            vc.serverType = serverType;
            vc.memberCenterFunctionId = MemberCenterFunctionTypeChiefClassroom; // 设置首席课堂功能ID
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    [LZRouter registerURLPattern:@"qcyzt://vipcentercollege" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            NSInteger requestType = [[routerParameters objectForKey:@"requestType"] integerValue];
            NSInteger contentId = [[routerParameters objectForKey:@"contentId"] integerValue];
            if (contentId > 0) {
                NSInteger contentId = [[routerParameters objectForKey:@"contentId"] integerValue];
                NSInteger contentType = [[routerParameters objectForKey:@"contentType"] integerValue];
                NSInteger requestType = [[routerParameters objectForKey:@"requestType"] integerValue];
                FMVIPXLZFDetailVC *vc = [[FMVIPXLZFDetailVC alloc] initWithType:requestType contentType:contentType];
                vc.contentId = contentId;
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            } else {
                JCStockPoolType poolType = [[routerParameters objectForKey:@"poolType"] integerValue];
                FMVIPXYItemTabVC *vc = [[FMVIPXYItemTabVC alloc] initWithType:requestType poolType:poolType];
                vc.title = [[routerParameters objectForKey:@"title"] URLDecodedString];
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            }
        }];
    }];
    
    // 我的卡券
    [LZRouter registerURLPattern:@"qcyzt://vouchers" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMCouponViewController *cardCoupon = [[FMCouponViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:cardCoupon animated:YES];
        }];
    }];
    [LZRouter registerURLPattern:@"qcyzt://coupon" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMCouponViewController *cardCoupon = [[FMCouponViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:cardCoupon animated:YES];
        }];
    }];
    
    // 我的收藏
    [LZRouter registerURLPattern:@"qcyzt://mycollection" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            NSInteger index = [[routerParameters objectForKey:@"index"] integerValue];
            FMMyCollectionViewController *collectVC = [[FMMyCollectionViewController alloc] init];
            collectVC.index = index;
            [[FMHelper getCurrentVC].navigationController pushViewController:collectVC animated:YES];
        }];
    }];
    
    // 已购服务
    [LZRouter registerURLPattern:@"qcyzt://purchasedservice" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMPurchasedServiceVC *vc = [[FMPurchasedServiceVC alloc] init];
            NSString *indexStr = [routerParameters objectForKey:@"index"];
            if (indexStr.length > 0) {
                vc.index = [indexStr integerValue];
            }
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 收支记录
    [LZRouter registerURLPattern:@"qcyzt://coinrecord" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            NSInteger index = [[routerParameters objectForKey:@"index"] integerValue];
            ConsumeHistoryViewController *consumeVC = [[ConsumeHistoryViewController alloc] init];
            consumeVC.index = index;
            [[FMHelper getCurrentVC].navigationController pushViewController:consumeVC animated:YES];
        }];
    }];
    
    // 消息
    [LZRouter registerURLPattern:@"qcyzt://message" toHandler:^(NSDictionary *routerParameters) {
        FMMessageViewController *messageVC = [[FMMessageViewController alloc]init];
        [[FMHelper getCurrentVC].navigationController pushViewController:messageVC animated:YES];
    }];
    
    // 邀请
    [LZRouter registerURLPattern:@"qcyzt://gotoshare" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            InviteRegisterViewController *vc = [[InviteRegisterViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 发票信息
    [LZRouter registerURLPattern:@"qcyzt://bill" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMMakeElectronicInvoiceViewController *vc = [[FMMakeElectronicInvoiceViewController alloc] init];
            vc.orderNo = [routerParameters objectForKey:@"id"];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 发票记录
    [LZRouter registerURLPattern:@"qcyzt://billhistory" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMElectronicInvoiceRecordViewController *vc = [[FMElectronicInvoiceRecordViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 反馈
    [LZRouter registerURLPattern:@"qcyzt://complaint" toHandler:^(NSDictionary *routerParameters) {
        NSString *contentId = [routerParameters objectForKey:@"id"];
        NSString *contentType = [routerParameters objectForKey:@"type"];
        NSString *complainFunction = [routerParameters objectForKey:@"complainFunction"];
        
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMComplaintViewController *vc = [[FMComplaintViewController alloc] init];
            vc.contentId = contentId;
            vc.contentType = contentType;
            vc.chooseFunction = complainFunction.URLDecodedString;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
     
    // 信息登记
    [LZRouter registerURLPattern:@"qcyzt://investorinfo" toHandler:^(NSDictionary *routerParameters) {
        NSString *reason = [routerParameters objectForKey:@"reason"];
        FMInvestorInfoViewController *invesVC = [[FMInvestorInfoViewController alloc] init];
        if ([reason length]) {
            invesVC.rejectionReason = reason;
        }
        [[FMHelper getCurrentVC].navigationController pushViewController:invesVC animated:YES];
    }];
    
    // 风险评估
    [LZRouter registerURLPattern:@"qcyzt://riskevaluation" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            UIViewController *currentVC = [FMHelper getCurrentVC];
            FMUserModel *userNewModel = [MyKeyChainManager load:kUserModel];
            
            BOOL reEvaluation = NO;
            if ([currentVC isMemberOfClass:[YTGOtherWebVC class]]) {
                YTGOtherWebVC *webVC = (YTGOtherWebVC *)currentVC;
                if ([webVC.startPage containsString:kAPI_UserCenter_CPDJ]) { // 如果是重新测评，current取外层VC
                    reEvaluation = YES;
                    currentVC = [currentVC.navigationController.viewControllers objectAtIndex:currentVC.navigationController.viewControllers.count - 2];
                    [currentVC.navigationController popViewControllerAnimated:NO];
                }
            }
            
            if (userNewModel.answerFlag == -1 || reEvaluation) { // 未测评 或者 重新测评
                FMRiskEvaluationReminderVC *vc = [[FMRiskEvaluationReminderVC alloc] init];
                vc.finishBlock = ^{
                    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                        YTGOtherWebVC *webVC;
                        webVC = [[YTGOtherWebVC alloc] init];
                        webVC.startPage = [NSString stringWithFormat:@"%@%@?versionCode=%@", prefix,kAPI_RiskEvalua_FXCP,APP_BUILD_VERSION];
                        webVC.titleStr = @"";
                        webVC.backBlock = ^{
                            [currentVC.navigationController popToViewController:currentVC animated:YES];
                        };
                        webVC.fxcpFinishBlock = ^(){
                            [currentVC.navigationController popToViewController:currentVC animated:YES];
                        };
                        
                        [currentVC.navigationController popViewControllerAnimated:NO];
                        [currentVC.navigationController pushViewController:webVC animated:YES];
                    }];
                };
                
                [currentVC.navigationController pushViewController:vc animated:YES];
            } else {
                [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                    YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
                    webVC.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_UserCenter_CPDJ];
                    webVC.titleStr = @"我的风险等级";
                    webVC.backBlock = ^{
                        [currentVC.navigationController popToViewController:currentVC animated:YES];
                    };
                    [currentVC.navigationController pushViewController:webVC animated:YES];
                }];
            }
        }];
    }];
    
    // 我的策略
    [LZRouter registerURLPattern:@"qcyzt://mystrategy" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMStockStrategyMineVC *vc = [[FMStockStrategyMineVC alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 包月服务
    [LZRouter registerURLPattern:@"qcyzt://userservice" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            FMPurchasedServiceVC *vc = [[FMPurchasedServiceVC alloc] init];
            NSString *type = [[routerParameters objectForKey:@"type"] URLDecodedString];
            if ([type isEqualToString:@"包月服务"]) {
                vc.index = 5;
            }
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    }];
    
    // 我的卡券-使用须知
    [LZRouter registerURLPattern:@"qcyzt://couponexplain" toHandler:^(NSDictionary *routerParameters) {
        FMWKWebViewController *webVC = [[FMWKWebViewController alloc] init];
        webVC.title = @"卡券使用须知";
        NSString *htmlPath = [[NSBundle mainBundle] pathForResource:@"CouponExplain" ofType:@"html"];
        if (htmlPath) {
            NSURL *fileURL = [NSURL fileURLWithPath:htmlPath];
            webVC.urlStr = fileURL.absoluteString;
        } else {
            NSLog(@"PointExplain.html文件未找到");
        }
        [[FMHelper getCurrentVC].navigationController pushViewController:webVC animated:YES];
    }];
    
    
#pragma mark -- 全局功能
    // web页
    [LZRouter registerURLPattern:@"http://" toHandler:^(NSDictionary *routerParameters) {
        [self openWebWithUrlStr:routerParameters[LZRouterParameterURL] title:nil];
    }];
    [LZRouter registerURLPattern:@"https://" toHandler:^(NSDictionary *routerParameters) {
        [self openWebWithUrlStr:routerParameters[LZRouterParameterURL] title:nil];
    }];
    [LZRouter registerURLPattern:@"qcyzt://web" toHandler:^(NSDictionary *routerParameters) {
        NSString *urlStr = [routerParameters[@"url"] URLDecodedString];
        NSString *title = [routerParameters[@"title"] URLDecodedString];
        if ([routerParameters[@"force"] integerValue]) {
            [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                [self openWebWithUrlStr:urlStr title:title];
            }];
        } else {
            [self openWebWithUrlStr:urlStr title:title];
        }
    }];
    
    // PDF
    [LZRouter registerURLPattern:@"qcyzt://pdf" toHandler:^(NSDictionary *routerParameters) {
        NSString *urlStr = [routerParameters[@"url"] URLDecodedString];
        NSString *title = [routerParameters[@"title"] URLDecodedString];
        FMPDFReaderViewController *vc = [[FMPDFReaderViewController alloc] init];
        vc.title = title.length ? title : @"阅读附件";
        vc.pdfUrl = urlStr;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 浏览图片
    [LZRouter registerURLPattern:@"qcyzt://browse" toHandler:^(NSDictionary *routerParameters) {
        NSArray *imgs = [JsonTool dicOrArrFromJsonString:[routerParameters[@"images"] URLDecodedString]];
        NSInteger position = [[routerParameters[@"position"] URLDecodedString] integerValue];
        
        HZPhotoBrowser *photoCtrl = [[HZPhotoBrowser alloc] init];
        photoCtrl.imageArray = imgs;
        photoCtrl.currentImageIndex = (int)position;
        [photoCtrl show];
    }];
    
    // 充值弹框
    [LZRouter registerURLPattern:@"qcyzt://recharge" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            BOOL isRechargeOpened = [[FMUserDefault getSeting:@"isRechargeOpened"] boolValue];
            if (isRechargeOpened) {
                [[FMPayTool payTool] gotoRecharge];
            }
        }];
    }]; 
    
    // 直接充值
    [LZRouter registerURLPattern:@"qcyzt://rechargedialog" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            if ([FMHelper getIAPPayStatus]) {
                // 内购
                [[FMPayTool payTool] goIAPPayViewControllerWithIsHaveBackBlock:NO currentVC:[FMHelper getCurrentVC]];
                return;
            }
            [[FMPayTool payTool] rechargeDialogWithProductId:routerParameters[@"productId"] payPrice:routerParameters[@"payPrice"] payInfo:routerParameters[@"payInfo"] type:routerParameters[@"type"] orderBlock:nil];
        }];
    }];
    
    // 分享
    [LZRouter registerURLPattern:@"qcyzt://share" toHandler:^(NSDictionary *routerParameters) {
        // 创建一个包含解码后参数的新字典
        NSMutableDictionary *decodedParams = [NSMutableDictionary dictionary];
        [routerParameters enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
            if ([obj isKindOfClass:[NSString class]]) {
                // 对字符串类型的值进行URL解码
                decodedParams[key] = [(NSString *)obj URLDecodedString];
            } else {
                // 非字符串类型直接保留原值
                decodedParams[key] = obj;
            }
        }];
        
        // 使用解码后的参数创建模型
        FMShareModel *model = [FMShareModel modelWithDictionary:decodedParams];
        if (!model.pic.length) {
            model.pic = kAPI_App_SharePic;
        }
        [FMShareHelper showShareViewWithModel:model];
    }];
    
    // 搜索页面
    [LZRouter registerURLPattern:@"qcyzt://search" toHandler:^(NSDictionary *routerParameters) {
        FMMainSearchViewController *search = [[FMMainSearchViewController alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:search animated:YES];
    }];
    
    // 普通用户进私信页面
    [LZRouter registerURLPattern:@"qcyzt://privateletter" toHandler:^(NSDictionary *routerParameters) {
        NSString *bigcastId = [routerParameters objectForKey:@"id"];
        if (bigcastId.length) {
            UIViewController *currentVC = [FMHelper getCurrentVC];
            if ([currentVC isMemberOfClass:[FMPrivateLetterViewController class]]) {
                FMPrivateLetterViewController *tmpVC = (FMPrivateLetterViewController *)currentVC;
                if ([tmpVC.model.bignameId isEqualToString:bigcastId]) {
                    return;
                }
            }
            
            [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                [HttpRequestTool requestPrivateLetterHomeWithBigcastId:bigcastId start:^{
                    [SVProgressHUD show];
                } failure:^{
                    [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                } success:^(NSDictionary *dic) {
                    if ([dic[@"status"] isEqualToString:@"1"]) {
                        [SVProgressHUD dismiss];
                        FMPrivateLetterBigcastHomeModel *model = [FMPrivateLetterBigcastHomeModel modelWithDictionary:dic[@"data"]];
                        if (model.historyRecord.count || model.currentSentence) {
                            FMPrivateLetterViewController *vc = [[FMPrivateLetterViewController alloc] init];
                            vc.model = model;
                            if ([currentVC isMemberOfClass:[FMPrivateLetterViewController class]]) {
                                FMPrivateLetterViewController *tmpVC = (FMPrivateLetterViewController *)currentVC;
                                if (![tmpVC.model.bignameId isEqualToString:bigcastId]) {
                                    [currentVC.navigationController pushViewController:vc animated:YES];
                                    NSMutableArray *controllers = [NSMutableArray arrayWithArray:currentVC.navigationController.viewControllers];
                                    [controllers removeObject:currentVC];
                                    currentVC.navigationController.viewControllers = [NSArray arrayWithArray:controllers];
                                }
                            } else {
                                if (currentVC.navigationController) {
                                    [currentVC.navigationController pushViewController:vc animated:YES];
                                } else {
                                    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
                                }
                            }
                        } else {
                            FMPrivateLetterNoRightViewController *vc = [[FMPrivateLetterNoRightViewController alloc] init];
                            vc.model = model;
                            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
                        }
                    } else {
                        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    }
                }];
            }];
        }
    }];
    
    // 投顾进入私信
    [LZRouter registerURLPattern:@"qcyzt://bignameprivateletter" toHandler:^(NSDictionary *routerParameters) {
        NSString *name = [[routerParameters objectForKey:@"name"] URLDecodedString];
        NSString *orderId = [routerParameters objectForKey:@"id"];
        FMBigcastPrivateLetterOrderListModel *model = [FMBigcastPrivateLetterOrderListModel new];
        model.speekerName = name;
        model.orderId = orderId;
        FMBigcastPrivateLetterViewController *vc = [[FMBigcastPrivateLetterViewController alloc] init];
        vc.model = model;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
    
    // 打开其它APP
    [LZRouter registerURLPattern:@"qcyzt://openapp" toHandler:^(NSDictionary *routerParameters) {
        NSURL *schemeUrl = [NSURL URLWithString:[routerParameters[@"scheme"] URLDecodedString]];
        BOOL canOpen = [[UIApplication sharedApplication] canOpenURL:schemeUrl];
        if (canOpen) {
            [[UIApplication sharedApplication] openURL:schemeUrl options:@{} completionHandler:nil];
        }
    }];
    
    // 微信客服
    [LZRouter registerURLPattern:@"qcyzt://wxkf" toHandler:^(NSDictionary *routerParameters) {
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            NSString *wechatAppidForLogin = [FMUserDefault getSeting:AppInit_LoginAppid];
            [WXApi registerApp:wechatAppidForLogin universalLink:UNIVERSAL_LINK];
            if (![WXApi isWXAppInstalled]) {
                [SVProgressHUD showInfoWithStatus:@"您还没有安装微信，请先安装"];
            }
            
            NSString *number = [routerParameters objectForKey:@"number"];
            FMAddWechatNumView *view = [[FMAddWechatNumView alloc] initAddWeChatNumViewWith:number];
            [view show];
        }];
    }];
    
    // 不是正在审核
    [LZRouter registerURLPattern:@"qcyzt://isnoaudit" toHandler:^(NSDictionary *routerParameters) {
        NSString *status = [routerParameters objectForKey:@"status"];
        [FMUserDefault setSeting:@"isNoAudit" Value:status];
    }];
}

// 一级页面跳转
+ (void)gotoFirstLevelVCWithIndex:(NSInteger)index innerIndex:(NSInteger)innerIndex params:(NSDictionary *)params{    
    if ([FMHelper getCurrentVC].navigationController == [[FMAppDelegate shareApp].main viewControllerAtIndex:index]) {
        [[FMAppDelegate shareApp].main.currentNav popToRootViewControllerAnimated:NO];
    } else {
        [[FMAppDelegate shareApp].main setSelectedIndex:index];
        if ([FMHelper getCurrentVC].navigationController.viewControllers.count > 1) {
            [[FMAppDelegate shareApp].main.currentNav popToRootViewControllerAnimated:NO];
        }
    }

    if (index == 0) {
        FMHomePageVC *vc = (FMHomePageVC *)[FMAppDelegate shareApp].main.currentNav.topViewController;
        if (innerIndex >= 0) {
            vc.index = innerIndex;
        }
        
        if ([params[@"browseTask"] boolValue]) {
            FMTaskConfigModel *userTaskProgress = [FMUserDefault getUnArchiverDataForKey:UserTaskProgressCacheKey];
            FMSubTask *task = userTaskProgress.taskDic[@(FMTaskTypeBrowseHomepage)];
            if (task.completeNum < task.taskNum) {
                [vc beginTask];
            }
        }
    }
}

// web页
+ (void)openWebWithUrlStr:(NSString *)urlStr title:(NSString *)title{
    if ([urlStr containsString:@"dgzq.com"]) {
        FMWKWebViewController *vc = [FMWKWebViewController new];
        vc.urlStr = urlStr;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        return;
    }
    
    if ([[urlStr lowercaseString] hasSuffix:@".pdf"]) {
        FMPDFReaderViewController *vc = [[FMPDFReaderViewController alloc] init];
        vc.title = title.length ? title : @"阅读附件";
        vc.pdfUrl = urlStr;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    } else {
        YTGNormalWebVC *webVC = [[YTGNormalWebVC alloc] init];
        webVC.startPage = urlStr;
        webVC.titleStr = title;
        [[FMHelper getCurrentVC].navigationController pushViewController:webVC animated:YES];
    }
}


@end
