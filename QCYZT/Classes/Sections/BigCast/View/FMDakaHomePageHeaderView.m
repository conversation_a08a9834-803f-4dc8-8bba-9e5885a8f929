//
//  FMDakaHomePageHeaderView.m
//  QCYZT
//
//  Created by shumi on 2022/6/27.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMDakaHomePageHeaderView.h"
#import "FMDakaHomePageBannerView.h"
#import "BigCastDetailModel.h"
#import "FMPersonDataInfoVC.h"
#import "FMMeInfoVC.h"
#import "FMMemberCenterProductViewController.h"
#import "YYImage.h"
#import "LZDoubleTextWidget.h"
#import "FMBigCastOperationRankViewController.h"
#import "SGAdvertScrollView.h"

@interface FMDakaHomePageHeaderView ()<SGAdvertScrollViewDelegate>

@property (nonatomic, strong) UIImageView *headerImg;
@property (nonatomic, strong) UILabel *nameLB;
@property (nonatomic, strong) UIImageView *iconImg;
@property (nonatomic, strong) UIView *livingView;
@property (nonatomic, strong) UIStackView *rightBtnStackView;
@property (nonatomic, strong) UIButton *vipBtn;
@property (nonatomic, strong) UIButton *askBtn;
@property (nonatomic, strong) FocusButton *focusBtn; // 关注按钮
@property (nonatomic, strong) UIButton *editBtn; // 编辑资料
@property (nonatomic, strong) UIButton *approveBtn; /// 认证UI
@property (nonatomic, strong) UIButton *detailBtn; /// 详细资料
@property (nonatomic, strong) UIView *operationRankView; /// 神操作排行
@property (nonatomic, strong) SGAdvertScrollView *adScrollView;
@property (nonatomic, strong) UIButton *profileIcon;
@property (nonatomic, strong) YYLabel  *profileLB; /// 简介
@property (nonatomic, strong) UIButton *certifiIcon;
@property (nonatomic, strong) UILabel  *certificateLB; /// 证书编号
@property (nonatomic, strong) UIButton *styleIcon;
//@property (nonatomic, strong) UILabel  *styleLB; /// 风格
@property(nonatomic,strong) ZLTagView *tagView;
@property (nonatomic, strong) UIStackView *itemStackView;
@property (nonatomic, strong) FMDakaHomePageBannerView *circleView;
@property (nonatomic, strong) UIView *sepLine;

@end

@implementation FMDakaHomePageHeaderView

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = FMClearColor;
    
    UIView *backView = [[UIView alloc] init];
    [self addSubview:backView];
    [backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(38));
        make.left.equalTo(@(15));
        make.size.equalTo(@(CGSizeMake(66, 66)));
    }];
    backView.backgroundColor = FMWhiteColor;
    UI_View_Radius(backView, 33);
//    UI_View_BorderRadius(backView, 34, 2, FMWhiteColor);

    // 头像
    UIImageView *headerImg = [[UIImageView alloc] init];
    headerImg.contentMode = UIViewContentModeScaleAspectFill;
    [backView addSubview:headerImg];
    [headerImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(@(0));
        make.size.equalTo(@(CGSizeMake(62, 62)));
    }];
    UI_View_Radius(headerImg, 31);
    self.headerImg = headerImg;
    
    UIImageView *iconImg = [[UIImageView alloc] init];
    [self addSubview:iconImg];
    [iconImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.right.equalTo(backView).offset(2);
        make.size.equalTo(@(CGSizeMake(20, 20)));
    }];
    self.iconImg = iconImg;
    
    // 直播中
    UIView *livingView = [[UIView alloc] init];
    [self addSubview:livingView];
    CGFloat liveHeight = [FMHelper isBigFont] ? 60 : 50;
    [livingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(headerImg);
        make.bottom.equalTo(headerImg).offset(5);
        make.width.equalTo(@(liveHeight));
        make.height.equalTo(@17);
    }];
    livingView.backgroundColor = ColorWithHex(0xF85A41);
    UI_View_Radius(livingView, 8.5);
    self.livingView = livingView;
    
    YYAnimatedImageView *gifV = [[YYAnimatedImageView alloc] init];
    gifV.image = [YYImage imageNamed:@"直播中.gif"];
    [livingView addSubview:gifV];
    [gifV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(@5);
        make.width.height.equalTo(@8);
    }];
    UILabel *zbzLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:10] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [livingView addSubview:zbzLabel];
    [zbzLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(gifV.mas_right).offset(3);
        make.centerY.equalTo(@0);
    }];
    zbzLabel.text = @"直播中";
    
    // 昵称
    UILabel *nameLB = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleBoldFont:19.0] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1];
    self.nameLB = nameLB;
    
    /// 认证UI
    UIButton *approveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [approveBtn setBackgroundColor:ColorWithHex(0xFFF0E3)];
    [approveBtn setTitleColor:ColorWithHex(0xFF7200) forState:UIControlStateNormal];
    approveBtn.titleLabel.font = [FMHelper scaleFont:12];
    UIImage *approvIcon = [UIImage imageWithTintColor:ColorWithHex(0xFF7200) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"usercenter_approve_icon")];
    [approveBtn setImage:approvIcon forState:UIControlStateNormal];
    approveBtn.userInteractionEnabled = NO;
    approveBtn.hidden = YES;
    UI_View_Radius(approveBtn, 12);
    [approveBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:3.5];
    self.approveBtn = approveBtn;

    UIStackView *nameApproveView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentLeading distribution:UIStackViewDistributionEqualSpacing spacing:7.5 arrangedSubviews:@[nameLB, approveBtn]];
    [self addSubview:nameApproveView];
    [nameApproveView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(headerImg.mas_right).offset(12);
        make.centerY.equalTo(headerImg);
    }];
    [approveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(@(CGSizeMake(87, 24)));
    }];

    UIStackView *rightBtnStackView = [[UIStackView alloc] init];
    rightBtnStackView.axis = UILayoutConstraintAxisHorizontal;
    rightBtnStackView.distribution = UIStackViewDistributionEqualSpacing;
    rightBtnStackView.spacing = 10;
    [self addSubview:rightBtnStackView];
    [rightBtnStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(headerImg);
        make.right.equalTo(@(-100));
        make.height.equalTo(@(30));
        make.left.equalTo(nameApproveView.mas_right).offset(15);
    }];
    self.rightBtnStackView = rightBtnStackView;
    
    UIButton *vipBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [vipBtn setTitle:@"开通VIP" forState:UIControlStateNormal];
    [vipBtn setTitleColor:ColorWithHex(0x7b3000) forState:UIControlStateNormal];
    vipBtn.titleLabel.font = [FMHelper scaleFont:14];
    [vipBtn addTarget:self action:@selector(vipBtnClick) forControlEvents:UIControlEventTouchUpInside];
    vipBtn.hidden = YES;
    [rightBtnStackView addArrangedSubview:vipBtn];
    CGSize vipBtnSize = [FMHelper isBigFont] ? CGSizeMake(85, 30) : CGSizeMake(70, 30);
    [vipBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(@(vipBtnSize));
    }];
    NSArray *colors =  @[(__bridge  id)ColorWithHex(0xFFE0A7).CGColor,(__bridge  id)ColorWithHex(0xFFC14D).CGColor];
    vipBtn.bounds = CGRectMake(0, 0, vipBtnSize.width, vipBtnSize.height);
    [vipBtn drawCAGradientWithcolors:colors];
    [vipBtn bringSubviewToFront:vipBtn.titleLabel];
    UI_View_Radius(vipBtn, 15);
    self.vipBtn = vipBtn;
    
    FocusButton *focusBtn = [FocusButton buttonWithType:UIButtonTypeCustom];
    focusBtn.size = CGSizeMake(75, 30);
    focusBtn.text = @"关注";
    focusBtn.focusText = @"已关注";
    focusBtn.textColor = FMNavColor;
    focusBtn.focusTextColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"add_focus")];
    focusBtn.focusImage = ImageWithName(@"");
    focusBtn.boardColor =  FMNavColor;
    focusBtn.focusBoardColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.titleLabel.font = [FMHelper scaleFont:15];
    [self addSubview:focusBtn];
    [focusBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(headerImg);
        make.right.equalTo(@(-15));
        make.size.equalTo(@(CGSizeMake(75, 30)));
    }];
    self.focusBtn = focusBtn;
    WEAKSELF
    focusBtn.compeletionClock = ^(NSString * _Nonnull dakaId, BOOL isfocus) {
        if ([dakaId isEqualToString:__weakSelf.model.userid]) {
            if (isfocus) {
               __weakSelf.model.userNoticerNums = [NSString stringWithFormat:@"%ld",__weakSelf.model.userNoticerNums.integerValue + 1];
            } else {
                __weakSelf.model.userNoticerNums = [NSString stringWithFormat:@"%ld",__weakSelf.model.userNoticerNums.integerValue - 1];
            }
            __weakSelf.model = __weakSelf.model;
        }
    };
    
    UIButton *editBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [editBtn setTitle:@"编辑资料" forState:UIControlStateNormal];
    [editBtn setTitleColor:FMNavColor forState:UIControlStateNormal];
    [editBtn setBackgroundColor:FMWhiteColor];
    editBtn.titleLabel.font = [FMHelper scaleFont:14];
    [editBtn addTarget:self action:@selector(editBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:editBtn];
    [editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(headerImg);
        make.right.equalTo(@(-15));
        make.size.equalTo(@(CGSizeMake(75, 30)));
    }];
    UI_View_BorderRadius(editBtn, 15, 1, FMWhiteColor);
    editBtn.hidden = YES;
    self.editBtn = editBtn;
    
    // 股神操作排行榜
    [self addSubview:self.operationRankView];
    [self.operationRankView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(approveBtn.mas_bottom).offset(0);
        make.height.equalTo(0);
    }];
    
    UIButton *profileIcon = [UIButton buttonWithType:UIButtonTypeCustom];
    [profileIcon setTitle:@"简介：" forState:UIControlStateNormal];
    [profileIcon setImage:ImageWithName(@"profile_icon") forState:UIControlStateNormal];
    [profileIcon setTitleColor:UIColor.up_textSecondaryColor forState:UIControlStateNormal];
    profileIcon.titleLabel.font = [FMHelper scaleFont:14.0];
    profileIcon.titleLabel.lineBreakMode = NSLineBreakByClipping;
    profileIcon.userInteractionEnabled = NO;
    [profileIcon setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self addSubview:profileIcon];
    [profileIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.operationRankView.mas_bottom).offset(15);
        make.left.equalTo(@(15));
        make.height.equalTo(@20);
    }];
    self.profileIcon = profileIcon;
    
    // 简介
    YYLabel *profileLB = [[YYLabel alloc] init];
    [self addSubview:profileLB];
    [profileLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(profileIcon);
        make.left.equalTo(75);
        make.right.equalTo(-15);
    }];
    profileLB.numberOfLines = 0;
    profileLB.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 90;
    self.profileLB = profileLB;
    
    /// 证书高度
    CGFloat certifiHeight = [FMHelper isBigFont] ? 30 : 20;
    UIButton *certifiIcon = [UIButton buttonWithType:UIButtonTypeCustom];
    [certifiIcon setTitle:@"证书：" forState:UIControlStateNormal];
    [certifiIcon setImage:ImageWithName(@"certificate_icon") forState:UIControlStateNormal];
    [certifiIcon setTitleColor:UIColor.up_textSecondaryColor forState:UIControlStateNormal];
    certifiIcon.titleLabel.font = [FMHelper scaleFont:14.0];
    certifiIcon.titleLabel.lineBreakMode = NSLineBreakByClipping;
    certifiIcon.userInteractionEnabled = NO;
    [certifiIcon setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self addSubview:certifiIcon];
    [certifiIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(profileIcon);
        make.top.equalTo(profileLB.mas_bottom).offset(6);
        make.height.equalTo(@(certifiHeight));
    }];
    self.certifiIcon = certifiIcon;
    
    // 证书编号
    UILabel *certificateLB = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:14.0] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1];
    [self addSubview:certificateLB];
    [certificateLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(certifiIcon.mas_right);
        make.right.equalTo(@(-15));
        make.top.equalTo(profileLB.mas_bottom).offset(6);
        make.height.equalTo(@(certifiHeight));
        make.width.priorityLow();
    }];
    self.certificateLB = certificateLB;
    
    
    // 风格
    CGFloat styleHeight = [FMHelper isBigFont] ? 30 : 20;
    UIButton *styleIcon = [UIButton buttonWithType:UIButtonTypeCustom];
    [styleIcon setTitle:@"风格：" forState:UIControlStateNormal];
    [styleIcon setImage:ImageWithName(@"style_icon") forState:UIControlStateNormal];
    [styleIcon setTitleColor:UIColor.up_textSecondaryColor forState:UIControlStateNormal];
    styleIcon.titleLabel.font = [FMHelper scaleFont:14.0];
    styleIcon.titleLabel.lineBreakMode = NSLineBreakByClipping;
    styleIcon.userInteractionEnabled = NO;
    [styleIcon setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self addSubview:styleIcon];
    self.styleIcon = styleIcon;
    
    [self addSubview:self.tagView];
    [styleIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(profileIcon);
        make.top.equalTo(certifiIcon.mas_bottom).offset(6);
        make.height.equalTo(@(styleHeight));
    }];
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(profileIcon.mas_right);
        make.right.equalTo(@(-15));
//        make.top.equalTo(certificateLB.mas_bottom).offset(6);
        make.centerY.equalTo(styleIcon.mas_centerY);
        make.height.equalTo(@(styleHeight));
    }];
    
    UIStackView *itemStackView = [[UIStackView alloc] init];
    itemStackView.axis = UILayoutConstraintAxisHorizontal;
    itemStackView.distribution = UIStackViewDistributionFillEqually;
    itemStackView.alignment = UIStackViewAlignmentFill;
    itemStackView.spacing = 0;
    [self addSubview:itemStackView];
    CGFloat itemStackHeight = [FMHelper isBigFont] ? 50.0f : 41.0f;
    [itemStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@(0));
        make.top.equalTo(self.styleIcon.mas_bottom).offset(10);
        make.height.equalTo(@(itemStackHeight));
    }];
    self.itemStackView = itemStackView;
    
    NSInteger itemCount = 4;
    for (NSInteger i = 0; i < itemCount; i ++) {
        LZDoubleTextWidget *dTextWidget = [[LZDoubleTextWidget alloc] init];
        dTextWidget.lab0.font = [FMHelper scaleBoldFont:20];
        dTextWidget.lab0.textColor = UIColor.up_textPrimaryColor;
        dTextWidget.lab1.font = [FMHelper scaleBoldFont:12];;
        dTextWidget.lab1.textColor = UIColor.up_textSecondary1Color;
        dTextWidget.tag = 1000 + i;

        [itemStackView addArrangedSubview:dTextWidget];

        WEAKSELF
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(UITapGestureRecognizer *sender) {
            [__weakSelf itemOption:sender];
        }];
        [dTextWidget addGestureRecognizer:tap];
        
        if (i < itemCount - 1) {
            UIView *line = [[UIView alloc] init];
            line.backgroundColor = ColorWithHex(0xede2e3);
            [dTextWidget addSubview:line];
            [line mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.centerY.equalTo(dTextWidget);
                make.size.equalTo(@(CGSizeMake(1, 25)));
            }];
        }
        
    }
    
    // 中线
    UIView *sepLine = [[UIView alloc] init];
    [self addSubview:sepLine];
    [sepLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(itemStackView.mas_bottom).offset(15);
        make.height.equalTo(@10);
        make.bottom.equalTo(@0);
    }];
    sepLine.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    self.sepLine = sepLine;
}

//  开通vip
- (void)vipBtnClick {
    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
        FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
        vc.bigcastId = self.model.userid;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
}

// 问股
- (void)askBtnClick {
    if (self.askBtnClickBlock) {
        self.askBtnClickBlock(self.askBtn);
    }
}

- (void)editBtnClick {
    FMMeInfoVC *vc = [[FMMeInfoVC alloc] init];
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

/// 投顾主页不做跳转
- (void)itemOption:(UITapGestureRecognizer *)sender {
    
}

- (void)detailBtnClick {
    FMPersonDataInfoVC *vc = [[FMPersonDataInfoVC alloc] init];
    vc.model = self.model;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

- (void)addSeeMoreButton {
    WEAKSELF
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"...展开"];
    [text yy_setColor:ColorWithHex(0x0074FA) range:[text.string rangeOfString:@"展开"]];
    text.yy_font = FontWithSize(14);
    [text yy_setTextHighlightRange:NSMakeRange(0, text.length) color:ColorWithHex(0x0074FA) backgroundColor:FMClearColor tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        __weakSelf.model.profileUnFold = !__weakSelf.model.profileUnFold;
        if (__weakSelf.refreshBlock) {
            __weakSelf.refreshBlock();
        }
    }];
    YYLabel *seeMore = [YYLabel new];
    seeMore.attributedText = text;
    [seeMore sizeToFit];
    NSAttributedString *truncationToken = [NSAttributedString yy_attachmentStringWithContent:seeMore contentMode:UIViewContentModeCenter attachmentSize:text.size  alignToFont:text.yy_font alignment:YYTextVerticalAlignmentCenter];
    self.profileLB.truncationToken = truncationToken;
}

- (UIImage *)getImageWithSize:(CGSize)size {
    CGFloat colors[] ={
        252/255.0f, 0/255.0f, 2/255.0f, 1,
        255/255.0f, 114/255.0f, 0/255.0f, 1
    };
    CGFloat locations[] = { 0.0, 1.0 };
    return [UIImage createGradientImageWithSize:size startPoint:CGPointMake(0, 0) endPoint:CGPointMake(size.width, 0) colors:colors locations:locations count:sizeof(colors)/(sizeof(colors[0])*4)];
}

#pragma  mark - SGAdvertScrollViewDelegate
- (void)advertScrollView:(SGAdvertScrollView *)advertScrollView didSelectedItemAtIndex:(NSInteger)index {
    if (self.model.textPropagandas.count || self.model.rank) {
        if (index < self.model.textPropagandas.count) {
            [ProtocolJump jumpWithUrl:self.model.textPropagandas[index].clickAction];
        } else {
            FMBigCastOperationRankViewController *vc = [[FMBigCastOperationRankViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
    }
}

#pragma mark - Getter/Setter
- (void)setModel:(BigCastDetailModel *)model {
    _model = model;
    [self.headerImg sd_setImageWithURL:[NSURL URLWithString:model.userIco] placeholderImage:ImageWithName(@"userCenter_dltx") options:SDWebImageRefreshCached completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        if (error) {
            NSLog(@"%@", error);
        }
    }];
    self.nameLB.text = model.userName;
    
    BOOL isMySelf = [model.userid isEqualToString:[FMUserDefault getUserId]];

    self.rightBtnStackView.hidden = isMySelf;
    self.detailBtn.hidden = isMySelf;
    self.focusBtn.hidden = isMySelf;
    // 认证
    self.approveBtn.hidden = (model.attestationType.integerValue == 0);
    if (model.attestationType.integerValue > 0) {
        [self.approveBtn setTitle:model.attestationTitle forState:UIControlStateNormal];
    }
    
    if (model.attestationType.integerValue == 1) {
        self.iconImg.image = ImageWithName(@"MemberCenter_AttestationTypeColumn");
    } else if (model.attestationType.integerValue == 2) {
        self.iconImg.image = ImageWithName(@"MemberCenter_AttestationTypeConsultant");
    } else {
        self.iconImg.image = ImageWithName(@"MemberCenter_AttestationTypeOrganization");
    }
    self.iconImg.hidden = (model.attestationType.integerValue == 0);

    if (model.attestationType.integerValue == 2) {
        if (model.isLiveNum == 1) {
            self.iconImg.hidden = YES;
            self.livingView.hidden = NO;
        } else {
            self.livingView.hidden = YES;
        }
    } else {
        self.livingView.hidden = YES;
    }
    
    if (isMySelf) { // 用户本人
        self.editBtn.hidden = NO;
    } else { // 非用户本人
        CGFloat detailBtnWidth = [FMHelper isBigFont] ? 75 : 65;
        if (model.attestationType.integerValue > 0) { // 已认证
            self.rightBtnStackView.hidden = !(model.attestationType.integerValue == 2);
            [self.detailBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.approveBtn);
                make.right.equalTo(@(-15));
                make.size.equalTo(@(detailBtnWidth));
            }];
        } else {  // 未认证
            self.rightBtnStackView.hidden = YES;
            [self.detailBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(self.headerImg);
                make.left.equalTo(self.headerImg.mas_right).offset(13.5);
                make.size.equalTo(@(detailBtnWidth));
            }];
        }
        
        self.focusBtn.dakaId = model.userid;
        self.focusBtn.isFocus = [[FMUserDataSyncManager sharedManager] isDakaNoticed:model.userid];
        NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
        // 是否打开开通vip入口
        BOOL bignameVipTag = [initDic[@"bignameVipTag"] boolValue];
        if (bignameVipTag) {
            self.vipBtn.hidden = (model.allowOpenVip.integerValue == 0 && model.attestationType.integerValue == 2) || [FMHelper getIAPPayStatus];
        } else {
            self.vipBtn.hidden = YES;
        }
        if (self.vipBtn.hidden) {
            [self.rightBtnStackView removeArrangedSubview:self.vipBtn];
        }
        if (model.allowOpenVip.integerValue == 2) {
            [self.vipBtn setTitle:@"已开通" forState:UIControlStateNormal];
        }
        self.askBtn.hidden = !([model.answerPrice floatValue] > 0) && (model.attestationType.integerValue == 2);
        if (self.askBtn.hidden) {
            [self.rightBtnStackView removeArrangedSubview:self.askBtn];
        }
    }
    
    if (model.userProfiles.length > 0) {
        self.profileIcon.hidden = NO;
        
        NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:model.userProfiles];
        [attributedString addAttributeFont:FontWithSize(14)];
        WEAKSELF
        [attributedString yy_setTextHighlightRange:NSMakeRange(0, model.userProfiles.length) color:UIColor.up_textSecondaryColor backgroundColor:FMClearColor tapAction:^(UIView *containerView, NSAttributedString *text, NSRange range, CGRect rect) {
            __weakSelf.model.profileUnFold = !__weakSelf.model.profileUnFold;
            if (__weakSelf.refreshBlock) {
                __weakSelf.refreshBlock();
            }
        }];
        self.profileLB.attributedText = attributedString;
        [self addSeeMoreButton];
        
        if (model.isProfileUnFold) {
            self.profileLB.numberOfLines = 0;
        } else {
            self.profileLB.numberOfLines = 2;
        }
    } else {
        self.profileIcon.hidden = YES;
        self.certifiIcon.hidden = true;
        [self.styleIcon mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.profileLB.mas_top);
        }];
//        [self.tagView mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.top.equalTo(self.profileLB.mas_top);
//        }];
        CGFloat styleHeight = [FMHelper isBigFont] ? 30 : 20;
        [self.tagView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.profileIcon.mas_right);
            make.right.equalTo(@(-15));
            make.centerY.equalTo(self.styleIcon);;
            make.height.equalTo(@(styleHeight));
        }];
    }
    
    if (model.userGoodAt.length > 0) {
        self.styleIcon.hidden = NO;
        self.tagView.titleArray = [[model.userGoodAt componentsSeparatedByString:@"、"] bk_reject:^BOOL(NSString * _Nonnull obj) {
            return !obj.length;
        }];
    } else {
        self.styleIcon.hidden = YES;
        [self.itemStackView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.tagView.mas_top);
        }];
    }
    
    if (self.model.certCode.length > 0 && model.userProfiles.length > 0) {
        self.certifiIcon.hidden = NO;
        self.certificateLB.text =  model.certCode;
        [self.styleIcon mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.certificateLB.mas_bottom).offset(6);
        }];
        [self.tagView mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.top.equalTo(self.certificateLB.mas_bottom).offset(6);
            make.centerY.equalTo(self.styleIcon);
        }];
    } else {
        self.certifiIcon.hidden = YES;
        [self.styleIcon mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.certificateLB.mas_top);
        }];
        [self.tagView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.styleIcon);
        }];
    }
    
    for (NSInteger i = 0; i < self.itemStackView.subviews.count; i ++) {
        LZDoubleTextWidget *dTextWidget = self.itemStackView.subviews[i];
        NSString *strNum, *strDescribe;
        if (i == 0) {
            if (model.userWriteNums >= 10000) {
                strNum = [NSString stringWithFormat:@"%.1fW",(model.userWriteNums / 10000.0)];
            } else {
                strNum = [NSString stringWithFormat:@"%ld",model.userWriteNums];
            }
            strDescribe = @"创作";
        }else{
            if(model.isTouGuVerify){ //投顾
                if (i == 1) {
                    strDescribe = @"粉丝";
                    if (model.userNoticerNums.integerValue >= 10000) {
                        strNum = [NSString stringWithFormat:@"%.1fW",(model.userNoticerNums.integerValue / 10000.0)];
                    } else {
                        strNum = [NSString stringWithFormat:@"%ld",model.userNoticerNums.integerValue];
                    }
                } else if (i == 2) {
                    strDescribe = @"获赞";
                    if (model.allSatisfiedNum.integerValue >= 10000.0) {
                        strNum = [NSString stringWithFormat:@"%.1fW",(model.allSatisfiedNum.integerValue / 10000.0)];
                    } else {
                        strNum = [NSString stringWithFormat:@"%ld",model.allSatisfiedNum.integerValue];
                    }
                } else {
                    strDescribe = @"累计打赏";
                    if (model.reward.integerValue >= 10000) {
                        strNum = [NSString stringWithFormat:@"%.1fW",(model.reward.integerValue / 10000.0)];
                    } else {
                        strNum = [NSString stringWithFormat:@"%ld",model.reward.integerValue];
                    }
                }
            }else{
                 if (i == 1) {
                    strDescribe = @"关注";
                    if (model.noticeNum >= 10000) {
                        strNum = [NSString stringWithFormat:@"%.1fW",(model.noticeNum / 10000.0)];
                    } else {
                        strNum = [NSString stringWithFormat:@"%ld",model.noticeNum];
                    }
                } else if (i == 2) {
                    strDescribe = @"粉丝";
                    if (model.userNoticerNums.integerValue >= 10000) {
                        strNum = [NSString stringWithFormat:@"%.1fW",(model.userNoticerNums.integerValue / 10000.0)];
                    } else {
                        strNum = [NSString stringWithFormat:@"%ld",model.userNoticerNums.integerValue];
                    }
                } else {
                    strDescribe = @"获赞";
                    if (model.allSatisfiedNum.integerValue >= 10000.0) {
                        strNum = [NSString stringWithFormat:@"%.1fW",(model.allSatisfiedNum.integerValue / 10000.0)];
                    } else {
                        strNum = [NSString stringWithFormat:@"%ld",model.allSatisfiedNum.integerValue];
                    }
                }
            }
        }
        
        dTextWidget.lab0.text = strNum;
        dTextWidget.lab1.text = strDescribe;
    }
    
    if (model.banners.count) {
        UIView *backView = [[UIView alloc] init];
        backView.backgroundColor = UIColor.up_contentBgColor;
        [self addSubview:backView];
        [backView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.top.equalTo(self.itemStackView.mas_bottom).offset(15);
//            make.height.equalTo(@(bannerHeight + 15));
        }];
        FMDakaHomePageBannerView *circleView = [[FMDakaHomePageBannerView alloc] init];
        circleView.layer.masksToBounds = YES;
        circleView.layer.cornerRadius = 5;
        [backView addSubview:circleView];
        UIEdgeInsets circleMargin = UIEdgeInsetsMake(0, 15, 15, 15);
        CGFloat bannerHeight = (UI_SCREEN_WIDTH - 15*2)*kBannerSizeRatio;
        [circleView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(bannerHeight);
            make.edges.mas_equalTo(backView).insets(circleMargin);
        }];
        circleView.detailModel = model;
    
        [self.sepLine mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.height.equalTo(@10);
            make.bottom.equalTo(@0);
            make.top.equalTo(backView.mas_bottom);
        }];
    } else {
        [self.sepLine mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.height.equalTo(@10);
            make.bottom.equalTo(@0);
            make.top.equalTo(self.itemStackView.mas_bottom).offset(15);
        }];
    }
    
    

    if (model.textPropagandas.count) {
        self.operationRankView.hidden = NO;
        [self.operationRankView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(22);
            make.top.equalTo(self.approveBtn.mas_bottom).offset(12);
        }];
        
        NSMutableArray *titleAttrStrs = [NSMutableArray array];
        [model.textPropagandas enumerateObjectsUsingBlock:^(BigCastDetailPropagateModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.tagName.length) {
                NSDictionary *dic = @{NSFontAttributeName:BoldFontWithSize(12), NSForegroundColorAttributeName:FMWhiteColor};
                CGSize signSize = [obj.tagName sizeWithAttr:dic andMaxSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)];
                CGSize signImgSize = CGSizeMake(signSize.width + 8, signSize.height + 4);
                UIImage *signBgImage = [self getImageWithSize:signImgSize];
                UIImage *signImage = [[signBgImage addTextWithImageSize:signImgSize text:obj.tagName textRect:CGRectMake(4, 2, signSize.width, signSize.height) textAttributes:dic] addCornerRadius:2];
                NSMutableAttributedString *attrString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@" %@", (obj.content.length ? obj.content : @"")] attributes:@{NSFontAttributeName : BoldFontWithSize(14), NSForegroundColorAttributeName : ColorWithHex(0xe42d00)}];
                [attrString insertAttributedString:[NSAttributedString yy_attachmentStringWithContent:signImage contentMode:UIViewContentModeCenter attachmentSize:signImgSize alignToFont:BoldFontWithSize(14) alignment:YYTextVerticalAlignmentCenter] atIndex:0];
                [titleAttrStrs addObject:attrString];
            } else {
                NSMutableAttributedString *attrString = [[NSMutableAttributedString alloc] initWithString:(obj.content.length ? obj.content : @"") attributes:@{NSFontAttributeName : BoldFontWithSize(14), NSForegroundColorAttributeName : ColorWithHex(0xe42d00)}];
                [titleAttrStrs addObject:attrString];
            }
        }];

        self.adScrollView.titleAttrStrs = titleAttrStrs;
    } else {
        self.operationRankView.hidden = YES;
        [self.operationRankView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(0);
            make.top.equalTo(self.approveBtn.mas_bottom).offset(0);
        }];
    }
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        CGFloat styleHeight = [FMHelper isBigFont] ? 24 : 18;
        CGFloat padding = [FMHelper isBigFont] ? 3 : 1;
        _tagView = [[ZLTagView alloc] initWithFrame:CGRectMake(0, 0, CGFLOAT_MAX, 0)];
        _tagView.tagLabelFont = [FMHelper scaleFont:12.0];
        _tagView.leftPadding = _tagView.rightPadding = 0;
        _tagView.middlePadding = 5.0f;
        _tagView.topPadding = padding;
        _tagView.tagLabelWidthPadding = 6;
        _tagView.labelHeight = styleHeight;
        _tagView.tagLabelCornerRadius = 2.0;
        _tagView.allowsSelection = YES;
        _tagView.tagLabelTextColor = UIColor.fm_daka_tag_textColor1;
        _tagView.tagLabelBgColor = UIColor.fm_daka_tag_bgColor;
        _tagView.numberofLines = 1;
        _tagView.userInteractionEnabled = false;
    }
    return _tagView;
}

- (UIView *)operationRankView {
    if (!_operationRankView) {
        _operationRankView = [UIView new];
        
        _adScrollView = [SGAdvertScrollView new];
        _adScrollView.scrollTimeInterval = 6.0f;
        _adScrollView.backgroundColor = FMClearColor;
        _adScrollView.imageTextPadding = 0;
        _adScrollView.delegate = self;
        [_operationRankView addSubview:_adScrollView];
        [_adScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsZero);
        }];
    }
    
    return _operationRankView;
}

@end
