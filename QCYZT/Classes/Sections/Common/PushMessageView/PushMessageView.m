//
//  PushMessageView.m
//  QCYZT
//
//  Created by 涂威 on 2017/8/2.
//  Copyright © 2017年 sdcf. All rights reserved.
//


#define MaxHeight 440.f
#define MarginX 15.f
#define ViewWidth 280.f
#define ButtonWidth 164.f
#define ButtonHeight 44.f
#define TitleHeight 50.f
#define MaxMessageHeight MaxHeight-TitleHeight-10.f-ButtonHeight-18.f*2
#define MinMessageHeight 50.f
#define ButtonSpacing 20.f

@interface PushMessageView ()

@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSAttributedString *message;
@property (nonatomic, strong) UIImage *noticeImage;
@property (nonatomic, copy) NSString *sureTitle;
@property (nonatomic, copy) NSString *cancelTitle;
@property (nonatomic, assign) BOOL clickSureDismiss;


@property (nonatomic, copy) void (^clickSure)();
@property (nonatomic, copy) void (^clickCancel)();

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIView *sepLine;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UILabel *messageLabel;
@property (nonatomic, strong) UIButton *sureButton;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) UIImageView *noticeImageView;
@property (nonatomic, strong) UIView *bottomView;

@property (nonatomic, assign) BOOL addMaskView; // 黑色蒙版
@property (nonatomic, strong) UIView *whiteView;
@property (nonatomic,assign) BOOL isCloseView;
@property (nonatomic, assign) BOOL useRedCancelButton; // 是否使用红色取消按钮

@property (nonatomic, strong) UIButton *closeBtn;


@end

@implementation PushMessageView

+ (id)showWithTitle:(NSString *)title
            message:(NSString *)message
        noticeImage:(UIImage *)noticeImage
          sureTitle:(NSString *)sureTitle
        cancelTitle:(NSString *)cancelTitle
          clickSure:(void (^)())clickSure
        clickCancel:(void (^)())clickCancel {
    PushMessageView *view = [[self alloc] initWithTitle:title
                                                message:[[NSAttributedString alloc] initWithString:message]
                                            noticeImage:noticeImage
                                              sureTitle:sureTitle
                                            cancelTitle:cancelTitle
                                              clickSure:clickSure
                                            clickSureDismiss:YES
                                            clickCancel:clickCancel
                                            addMaskView:NO
                                      useRedCancelButton:NO];
    [view show];
    return view;
}

+ (id)showWithTitle:(NSString *)title
              attrMessage:(NSAttributedString *)attrMessage
          noticeImage:(UIImage *)noticeImage
            sureTitle:(NSString *)sureTitle
          cancelTitle:(NSString *)cancelTitle
            clickSure:(void(^)())clickSure
        clickCancel:(void(^)())clickCancel {
    PushMessageView *view = [[self alloc] initWithTitle:title
                                                message:attrMessage
                                            noticeImage:noticeImage
                                              sureTitle:sureTitle
                                            cancelTitle:cancelTitle
                                              clickSure:clickSure
                                            clickSureDismiss:YES
                                            clickCancel:clickCancel
                                            addMaskView:NO
                                      useRedCancelButton:NO];
    [view show];
    return view;
}

+ (id)showCloseWithTitle:(NSString *)title
              message:(NSString *)message
          noticeImage:(UIImage *)noticeImage
            sureTitle:(NSString *)sureTitle
               clickSure:(void(^)())clickSure {
    PushMessageView *view = [[self alloc] initWithTitle:title
                                                message:[[NSAttributedString alloc] initWithString:message]
                                            noticeImage:noticeImage
                                              sureTitle:sureTitle
                                            cancelTitle:nil
                                              clickSure:clickSure
                                       clickSureDismiss:YES
                                            clickCancel:nil
                                            addMaskView:NO
                                      useRedCancelButton:NO];
    view.closeBtn.hidden = NO;
    view.messageLabel.textAlignment = NSTextAlignmentCenter;
    [view show];
    return view;
}

+ (id)showWithTitle:(NSString *)title
              message:(NSString *)message
          noticeImage:(UIImage *)noticeImage
            sureTitle:(NSString *)sureTitle
          cancelTitle:(NSString *)cancelTitle
            clickSure:(void(^)())clickSure
     clickSureDismiss:(BOOL)clickSureDismiss
          clickCancel:(void(^)())clickCancel
        addMaskView:(BOOL)addMaskView
{
    PushMessageView *view = [[self alloc] initWithTitle:title
                                                message:[[NSAttributedString alloc] initWithString:message]
                                            noticeImage:noticeImage
                                              sureTitle:sureTitle
                                            cancelTitle:cancelTitle
                                              clickSure:clickSure
                                       clickSureDismiss:clickSureDismiss
                                            clickCancel:clickCancel
                                            addMaskView:addMaskView
                                      useRedCancelButton:NO];
    [view show];
    return view;
}

+ (id)showWithTitle:(NSString *)title
              message:(NSString *)message
          noticeImage:(UIImage *)noticeImage
            sureTitle:(NSString *)sureTitle
          cancelTitle:(NSString *)cancelTitle
            clickSure:(void(^)())clickSure
        clickCancel:(void(^)())clickCancel
      redCancelButton:(BOOL)redCancelButton
{
    PushMessageView *view = [[self alloc] initWithTitle:title
                                                message:[[NSAttributedString alloc] initWithString:message]
                                            noticeImage:noticeImage
                                              sureTitle:sureTitle
                                            cancelTitle:cancelTitle
                                              clickSure:clickSure
                                       clickSureDismiss:YES
                                            clickCancel:clickCancel
                                            addMaskView:NO
                                      useRedCancelButton:redCancelButton];
    [view show];
    return view;
}

- (instancetype)initWithTitle:(NSString *)title
                      message:(NSAttributedString *)message
                  noticeImage:(UIImage *)noticeImage
                    sureTitle:(NSString *)sureTitle
                  cancelTitle:(NSString *)cancelTitle
                    clickSure:(void (^)())clickSure
             clickSureDismiss:(BOOL)clickSureDismiss
                  clickCancel:(void (^)())clickCancel
                  addMaskView:(BOOL)addMaskView
            useRedCancelButton:(BOOL)useRedCancelButton{
    if (self = [super init]) {
        self.title            = title;
        self.message          = message;
        self.noticeImage      = noticeImage;
        self.sureTitle        = sureTitle;
        self.cancelTitle      = cancelTitle;
        self.clickSure        = clickSure;
        self.clickSureDismiss = clickSureDismiss;
        self.clickCancel      = clickCancel;
        self.addMaskView      = addMaskView;
        self.useRedCancelButton = useRedCancelButton;

        [self setupViews];
    }
    return self;
}

- (void)show {
    [[FMPopWindowManager shareManager].mutableArr addObject:self];
    [[FMPopWindowManager shareManager] show];
}

- (void)dealloc {
    FMLog(@"%s", __func__);
}

- (void)layoutSubviews  {
    [super layoutSubviews];
    if (!self.isCloseView) {
        self.frame = CGRectMake(self.frame.origin.x, self.frame.origin.y, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
        if (self.whiteView.height > UI_SCREEN_HEIGHT) {
            if (self.title.length >  0) {
                self.scrollView.height = UI_SCREEN_HEIGHT - 30 - TitleHeight - 80;
            } else {
                self.scrollView.height = UI_SCREEN_HEIGHT - 30 - 80;
            }
            self.whiteView.height  = UI_SCREEN_HEIGHT - 30;
            self.bottomView.frame = CGRectMake(0, self.scrollView.bottom, self.whiteView.width, 80.f);
        }
        self.whiteView.center = CGPointMake(UI_SCREEN_WIDTH/2.f, UI_SCREEN_HEIGHT/2.f);

    }
}

#pragma mark - Private Method
- (void)setupViews {
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    self.backgroundColor = FMClearColor;
    if (self.addMaskView) {
        UIView *maskView = [[UIView alloc] initWithFrame:CGRectMake(0, UI_SAFEAREA_TOP_HEIGHT, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)];
        [self addSubview:maskView];
        maskView.backgroundColor = UIColor.up_textSecondaryColor;
    }
    
    UIView *whiteView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, ViewWidth, 0)];
    whiteView.backgroundColor = UIColor.up_contentBgColor;
    whiteView.layer.cornerRadius = 15;
    [self addSubview:whiteView];
    self.whiteView = whiteView;
    
    if (self.noticeImage) {
        [whiteView addSubview:self.noticeImageView];
        self.noticeImageView.size = CGSizeMake(whiteView.width, whiteView.width*self.noticeImageView.height/self.noticeImageView.width);
        self.noticeImageView.bottom = 20;
        self.noticeImageView.centerX = whiteView.width/2.f;
    }

    if (self.title.length) {
        [whiteView addSubview:self.titleLabel];
        [whiteView addSubview:self.sepLine];
        
        self.titleLabel.height = TitleHeight;
        self.titleLabel.width = whiteView.width-MarginX*2.f;
        self.titleLabel.top = 0.f;
        self.titleLabel.centerX = whiteView.width/2.f;

        self.sepLine.frame = CGRectMake(0, self.titleLabel.bottom, whiteView.width, 0.5f);
    }
    
    [whiteView addSubview:self.scrollView];
    [self.scrollView addSubview:self.messageLabel];
    [whiteView addSubview:self.bottomView];
    [self.bottomView addSubview:self.sureButton];
    if (self.cancelTitle) [self.bottomView addSubview:self.cancelButton];
    
    CGSize fixSize = [self.messageLabel sizeThatFits:CGSizeMake(whiteView.width-MarginX*2.f, MAXFLOAT)];
    self.messageLabel.height = MAX(MinMessageHeight, fixSize.height);
    self.messageLabel.width = fixSize.width;
    self.messageLabel.top = 0;
    self.messageLabel.centerX = whiteView.width/2.f;
    
    self.scrollView.contentSize = CGSizeMake(whiteView.width, self.messageLabel.height);
    self.scrollView.width = whiteView.width;
    self.scrollView.height = MIN(MaxMessageHeight, self.scrollView.contentSize.height);
    self.scrollView.top = self.title.length ? self.titleLabel.bottom+10.f : 20.0f;
    self.scrollView.centerX = whiteView.width/2.f;
    
    self.bottomView.frame = CGRectMake(0, self.scrollView.bottom, whiteView.width, 80.f);
    if (self.cancelTitle) {
        self.sureButton.size = self.cancelButton.size = CGSizeMake((whiteView.width-ButtonSpacing*3.f)/2.f, ButtonHeight);
        self.sureButton.centerY = self.cancelButton.centerY = self.bottomView.height/2.f;
        self.cancelButton.left = ButtonSpacing;
        self.sureButton.left = self.cancelButton.right+ButtonSpacing;
    } else {
        self.sureButton.size = CGSizeMake(ButtonWidth, ButtonHeight);
        self.sureButton.center = CGPointMake(self.bottomView.width/2.f, self.bottomView.height/2.f);
    }
    
    whiteView.height = self.bottomView.bottom;
    whiteView.center = CGPointMake(UI_SCREEN_WIDTH/2.f, UI_SCREEN_HEIGHT/2.f);
    
    UIButton *closeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"xyhlb_close") target:self action:@selector(clickCancel:)];
    [self addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(whiteView.mas_bottom).offset(10);
        make.centerX.equalTo(@0);
        make.width.height.equalTo(@30);
    }];
    closeBtn.hidden = YES;
    self.closeBtn = closeBtn;
}

- (void)showWithAnimateInView:(UIView *)superView {
    [superView addSubview:self];
    
    self.alpha = 0;
    self.sureButton.userInteractionEnabled = NO;
    self.cancelButton.userInteractionEnabled = NO;
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveLinear animations:^{
        self.alpha = 1.0;
    } completion:^(BOOL finished) {
        self.sureButton.userInteractionEnabled = YES;
        self.cancelButton.userInteractionEnabled = YES;
    }];
    
    CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
    animation.values = @[@(0.8), @(1.05), @(1.1), @(1)];
    animation.keyTimes = @[@(0), @(0.3), @(0.5), @(1.0)];
    animation.timingFunctions = @[[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                  [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                  [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                  [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear]];
    animation.duration = 0.3f;
    [self.layer addAnimation:animation forKey:@"bouce"];
}

- (void)hideWithCompletion:(void(^)(void))completion{
    [[FMPopWindowManager shareManager] dismiss:self];
    if (completion) {
        completion();
    }
}


#pragma mark - Respond Method
- (void)clickSure:(UIButton *)sender {
    WEAKSELF
    if (self.clickSureDismiss) {
        [self hideWithCompletion:^{
            if (__weakSelf.clickSure) __weakSelf.clickSure();
        }];
    } else {
        if (__weakSelf.clickSure) __weakSelf.clickSure();
    }
    self.isCloseView = YES;
}

- (void)clickCancel:(UIButton *)sender {
    WEAKSELF
    [self hideWithCompletion:^{
        if (__weakSelf.clickCancel) __weakSelf.clickCancel();
    }];
    self.isCloseView = YES;
}

#pragma mark - Setter/Getter
- (UILabel *)titleLabel {
    if (_titleLabel == nil) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont boldSystemFontOfSize:20];
        _titleLabel.text = self.title;
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UIView *)sepLine {
    if (_sepLine == nil) {
        _sepLine = [[UIView alloc] init];
        _sepLine.backgroundColor = UIColor.fm_sepline_color;
    }
    return _sepLine;
}

- (UIScrollView *)scrollView {
    if (_scrollView == nil) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsHorizontalScrollIndicator = NO;
    }
    return _scrollView;
}

- (UILabel *)messageLabel {
    if (_messageLabel == nil) {
        _messageLabel = [[UILabel alloc] init];
        _messageLabel.font = [UIFont systemFontOfSize:16];
        _messageLabel.textColor = UIColor.up_textSecondaryColor;
        _messageLabel.numberOfLines = 0;
        
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithAttributedString:self.message];
        NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
        style.lineSpacing = 5.f;
        [attrStr addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, attrStr.length)];
        _messageLabel.attributedText = attrStr;
    }
    return _messageLabel;
}

- (UIView *)bottomView {
    if (_bottomView == nil) {
        _bottomView = [[UIView alloc] init];
    }
    return _bottomView;
}

- (UIButton *)sureButton {
    if (_sureButton == nil) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _sureButton.backgroundColor = FMNavColor;
        [_sureButton setTitle:self.sureTitle forState:UIControlStateNormal];
        [_sureButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _sureButton.layer.cornerRadius = ButtonHeight/2.f;
        _sureButton.layer.masksToBounds = YES;
        [_sureButton addTarget:self action:@selector(clickSure:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _sureButton;
}

- (UIButton *)cancelButton {
    if (_cancelButton == nil) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelButton setTitle:self.cancelTitle forState:UIControlStateNormal];

        // 根据useRedCancelButton属性设置不同的颜色
        if (self.useRedCancelButton) {
            [_cancelButton setTitleColor:FMRedColor forState:UIControlStateNormal];
            _cancelButton.layer.borderColor = FMRedColor.CGColor;
        } else {
            [_cancelButton setTitleColor:ColorWithHex(0xd6d6d6) forState:UIControlStateNormal];
            _cancelButton.layer.borderColor = ColorWithHex(0xd6d6d6).CGColor;
        }

        _cancelButton.layer.cornerRadius = ButtonHeight/2.f;
        _cancelButton.layer.borderWidth = 1.f;
        _cancelButton.layer.masksToBounds = YES;
        [_cancelButton addTarget:self action:@selector(clickCancel:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (UIImageView *)noticeImageView {
    if (_noticeImageView == nil) {
        _noticeImageView = [[UIImageView alloc] initWithImage:self.noticeImage];
    }
    return _noticeImageView;
}

@end

