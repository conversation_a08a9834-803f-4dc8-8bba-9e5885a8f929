//
//  PushMessageView.h
//  QCYZT
//
//  Created by 涂威 on 2017/8/2.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface PushMessageView : UIView

/**
 警告弹窗，推送消息、版本更新、自选股删除等有使用到
 当只需要一个按钮时，cancelTitle、clickCancel传入nil
 当不需要图片时 noticeimage传入nil
 
 @param title             标题
 @param message           文本内容
 @param noticeImage       提示图片
 @param messageAlignment  文本对齐方式
 @param sureTitle         确定按钮title
 @param cancelTitle       取消按钮title
 @param clickSure         确定按钮点击事件
 @param clickCancel       取消按钮点击事件
 */
+ (id)showWithTitle:(NSString *)title
            message:(NSString *)message
        noticeImage:(UIImage *)noticeImage
          sureTitle:(NSString *)sureTitle
        cancelTitle:(NSString *)cancelTitle
          clickSure:(void(^)())clickSure
        clickCancel:(void(^)())clickCancel;

+ (id)showWithTitle:(NSString *)title
        attrMessage:(NSAttributedString *)attrMessage
        noticeImage:(UIImage *)noticeImage
          sureTitle:(NSString *)sureTitle
        cancelTitle:(NSString *)cancelTitle
          clickSure:(void(^)())clickSure
        clickCancel:(void(^)())clickCancel;

+ (id)showCloseWithTitle:(NSString *)title
                 message:(NSString *)message
             noticeImage:(UIImage *)noticeImage
               sureTitle:(NSString *)sureTitle
               clickSure:(void(^)())clickSure;

+ (id)showWithTitle:(NSString *)title
            message:(NSString *)message
        noticeImage:(UIImage *)noticeImage
          sureTitle:(NSString *)sureTitle
        cancelTitle:(NSString *)cancelTitle
          clickSure:(void(^)())clickSure
   clickSureDismiss:(BOOL)clickSureDismiss
        clickCancel:(void(^)())clickCancel
        addMaskView:(BOOL)addMaskView;

/**
 警告弹窗，支持红色取消按钮
 
 @param title             标题
 @param message           文本内容
 @param noticeImage       提示图片
 @param sureTitle         确定按钮title
 @param cancelTitle       取消按钮title
 @param clickSure         确定按钮点击事件
 @param clickCancel       取消按钮点击事件
 @param redCancelButton   是否使用红色取消按钮
 */
+ (id)showWithTitle:(NSString *)title
            message:(NSString *)message
        noticeImage:(UIImage *)noticeImage
          sureTitle:(NSString *)sureTitle
        cancelTitle:(NSString *)cancelTitle
          clickSure:(void(^)())clickSure
        clickCancel:(void(^)())clickCancel
    redCancelButton:(BOOL)redCancelButton;


// 常规初始化方法禁用
- (instancetype)init UNAVAILABLE_ATTRIBUTE;
+ (instancetype)new UNAVAILABLE_ATTRIBUTE;


@end

