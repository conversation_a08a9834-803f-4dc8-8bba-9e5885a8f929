//
//  FMRealtimeDragonTigerViewController.m
//  QCYZT
//
//  Created by Cursor on 2024-12-19
//  Copyright © 2024 Cursor. All rights reserved.
//

#import "FMRealtimeDragonTigerViewController.h"
#import "FMRealtimeDragonTigerIndexView.h"
#import "FMRealtimeDragonTigerControlView.h"
#import "FMRealTimeDragonTigerStockInfoCell.h"
#import "FMRealtimeDragonTigerDragView.h"
#import "HttpRequestTool+Stock.h"
#import "MessageThrottle.h"
#import "FMGuideViewController.h"
#import "FMNewGuideViewController.h"
#import "RealTimeDragonSortTool.h"


@interface FMRealtimeDragonTigerViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *closeBtnView;
@property (nonatomic, assign) BOOL closeBtnViewHidden; // 是否隐藏关闭按钮视图
@property (nonatomic, strong) FMRealtimeDragonTigerDragView *dateDragView;
@property (nonatomic, strong) FMRealtimeDragonTigerIndexView *indexView;
@property (nonatomic, strong) FMRealtimeDragonTigerControlView *controlView;
@property (nonatomic, strong) FMRealTimeDragonTigerStockInfoCell *stockCell;

@property (nonatomic, assign) BOOL isSelfStock;
@property (nonatomic, assign) BOOL isKC;
@property (nonatomic, assign) BOOL isST;
@property (nonatomic, copy) NSString *chooseDate;
@property (nonatomic, copy) NSString *startTime;
@property (nonatomic, copy) NSString *endTime;

// 排序参数
@property (nonatomic, assign) BOOL orderType;               // 排序类型 0 正序 1倒叙
@property (nonatomic, assign) NSInteger orderParam;         // 排序字段

@property (nonatomic, strong) NSArray<FMRealTimeDragonTigerStockInfoModel *> *pools;
@property (nonatomic, strong) NSArray<FMRealTimeDragonTigerStockRangeModel *> *rangePools;

// 分页加载相关属性
@property (nonatomic, assign) NSInteger totalCount;                              // 总数据量
@property (nonatomic, assign) NSInteger beginIndex;                              // 可见区域起始索引
@property (nonatomic, assign) NSInteger endIndex;                            // 可见区域终点索引
@property (nonatomic, assign) NSInteger needScrollTop;                       // 需要滚动到顶部

@property (nonatomic, strong) UPMarketMonitor *monitor;                          // 请求数据定时器

@end

@implementation FMRealtimeDragonTigerViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupViews];
    
    [self mt_limitSelector:@selector(request)
            oncePerDuration:0.5
                   usingMode:MTPerformModeDebounce];

    [self mt_limitSelector:@selector(handleScrollStoppedWithStartIndex:endIndex:)
            oncePerDuration:0.5
                   usingMode:MTPerformModeDebounce];

    // 添加UI刷新防抖
    [self mt_limitSelector:@selector(reloadTableViewData)
            oncePerDuration:0.1
                   usingMode:MTPerformModeDebounce];
    
    
    UIButton *jumpBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"sslhb_jump") target:self action:@selector(jumpToLHB)];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:jumpBtn];
}

- (void)setupViews {
    self.view.backgroundColor = UIColor.up_contentBgColor;
    self.title = @"实时龙虎榜";
    
    // 添加UIStackView
    [self.view addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.height.equalTo(UI_SCREEN_HEIGHT - ceil(UI_SAFEAREA_TOP_HEIGHT + UI_SAFEAREA_BOTTOM_HEIGHT));
        make.top.equalTo(0);
    }];
    
    [self.stackView addArrangedSubview:self.tableView];
    [self.stackView addArrangedSubview:self.closeBtnView];
    [self.stackView addArrangedSubview:self.dateDragView];
    
    [self.closeBtnView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(18);
    }];
    
    [self.dateDragView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(60);
    }];
    
    self.closeBtnView.hidden = self.closeBtnViewHidden = YES;
    self.dateDragView.hidden = NO;
    
    self.isSelfStock = NO;
    self.isKC = YES;
    self.isST = NO;

    self.orderType = YES;  // 1倒叙 0正序
    self.orderParam = 2;   // 2 主力净额（实时列表默认排序）
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavRedColor];
    [self request];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
        
    [self.indexView viewDidAppear];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self showGuideViewIfNeeded];
    });
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];

    [self.indexView viewDidDisappear];
    [self.monitor stopMonitor];

    // 内存优化：页面消失时清理不必要的数据
    if (self.isMovingFromParentViewController) {
        // 页面被pop时，清理大数据
        self.pools = nil;
        self.rangePools = nil;
    }
}

- (void)scrollViewDidScroll:(UITableView *)tableView {
    CGRect rect = [tableView rectForHeaderInSection:0];
    BOOL RealtimeDragonTigerViewScrollEnable = tableView.contentOffset.y >= rect.origin.y;
//    NSLog(@"实时龙虎榜滚动位置: %f --- %f --- %zd", tableView.contentOffset.y, rect.origin.y, RealtimeDragonTigerViewScrollEnable);

    [[NSNotificationCenter defaultCenter] postNotificationName:@"RealtimeDragonTigerViewScrollEnable" object:@(RealtimeDragonTigerViewScrollEnable) userInfo:nil];
}

#pragma mark - UITableViewDelegate/DataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (!self.startTime) {
        self.stockCell.stockModels = self.pools;
    } else {
        self.stockCell.rangeModels = self.rangePools;
    }
    
    return self.stockCell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UI_SCREEN_HEIGHT - ceil(UI_SAFEAREA_TOP_HEIGHT + UI_SAFEAREA_BOTTOM_HEIGHT + 80 + [self calculateBottomViewHeight]);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return self.controlView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 80;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)calculateBottomViewHeight {
    CGFloat bottomViewHeight = 0;
    if (self.closeBtnView.hidden == NO) { // 这个地方不能用closeBtnViewHidden判断
        bottomViewHeight += 18;
    }
    if (self.dateDragView.hidden == NO) {
        bottomViewHeight += 60;
    }
    
    return bottomViewHeight;
}

#pragma mark - HTTP
- (void)requestFromZero {
    self.beginIndex = 0;
    self.endIndex = 15;
    self.needScrollTop = YES;
    
    [self request];
}

- (void)request {
    [self.monitor stopMonitor];
    UPMarketKLineDataReq * req = [[UPMarketKLineDataReq alloc] initWithSetCode:1 code:@"000001"];
    req.wantNum = 1;
    WEAKSELF
    [self.monitor startMonitorStockKLineData:req tag:0 completionHandler:^(UPMarketKLineDataRsp *rsp, NSError *error) {
        [__weakSelf updateDragViewDate];
        
        if (!__weakSelf.startTime) {
            [__weakSelf requestData];
        } else {
            [__weakSelf requestRangeData];
        }
    }];
}

- (void)requestData {
    // 首次请求获取total字段和少量数据
    [HttpRequestTool requestRealTimeWinnerStockListWithStartIndex:self.beginIndex endIndex:self.endIndex date:self.chooseDate showKc:self.isKC showSt:self.isST isSelfStock:self.isSelfStock orderType:self.orderType orderParam:self.orderParam start:^{
    } failure:^{
        //            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if (lz_HttpStatusCheck(dic)) {
            self.totalCount = [dic[@"data"][@"total"] integerValue];
            [self generatePlaceholderDataWithCount:self.totalCount isRangeData:NO];

            NSArray<FMRealTimeDragonTigerStockInfoModel *> *datas = [NSArray modelArrayWithClass:[FMRealTimeDragonTigerStockInfoModel class] json:dic[@"data"][@"dataList"]];

            [self requestUPRealTimeDataWithServerData:datas range:NSMakeRange(self.beginIndex, datas.count) isRangeData:NO];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

// 区间统计
- (void)requestRangeData {
    NSString *startTime = [NSString stringWithFormat:@"%@ %@:00", self.chooseDate, self.startTime];
    NSString *endTime = [NSString stringWithFormat:@"%@ %@:00", self.chooseDate, self.endTime];
    // 首次请求获取total字段和少量数据
    [HttpRequestTool requestRealTimeWinnerStockRangeStatisticWithStartIndex:self.beginIndex endIndex:self.endIndex startTime:startTime endTime:endTime showKc:self.isKC showSt:self.isST isSelfStock:self.isSelfStock orderType:self.orderType orderParam:self.orderParam start:^{
    } failure:^{
//        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if (lz_HttpStatusCheck(dic)) {
            self.totalCount = [dic[@"data"][@"total"] integerValue];
            // 生成占位符数据
            [self generatePlaceholderDataWithCount:self.totalCount isRangeData:YES];

            NSArray<FMRealTimeDragonTigerStockRangeModel *> *datas = [NSArray modelArrayWithClass:[FMRealTimeDragonTigerStockRangeModel class] json:dic[@"data"][@"dataList"]];

            [self requestUPRealTimeDataWithServerData:datas range:NSMakeRange(self.beginIndex, datas.count) isRangeData:YES];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

// 向优品请求数据
- (void)requestUPRealTimeDataWithServerData:(NSArray *)serverData range:(NSRange)range isRangeData:(BOOL)isRangeData {
    NSArray<UPHqStockUnique *> *stockArray = [self getReqRangeArrayFromData:serverData];

    NSDate *selectedDate = [NSDate dateFromFormatedString:self.chooseDate format:@"yyyy-MM-dd"];
    if (![selectedDate isSameDayWithDate:[NSDate date]] || stockArray.count == 0) {
        // 非当天或者没有需要请求UP数据的股票，异步处理数据并更新界面
        [self processDataAsyncAndUpdate:serverData range:range isRangeData:isRangeData];
        return;
    }

    UPMarketOptStockHqReq *hqReq = [[UPMarketOptStockHqReq alloc] initWithStockArray:stockArray];
    WEAKSELF
    [UPMarketManager requestOptStockHq:hqReq completionHandler:^(UPMarketOptStockHqRsp *rsp, NSError *error) {
        if (!error && rsp.dataArray.count > 0) {
            // 异步处理UP数据更新和排序
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                NSArray *updatedData = [__weakSelf updateServerData:serverData withUPData:rsp.dataArray isRangeData:isRangeData];
                dispatch_async(dispatch_get_main_queue(), ^{
                    [__weakSelf updateDataAtRange:range withData:updatedData isRangeData:isRangeData];
                });
            });
        } else {
            // UP数据请求失败，异步处理服务器原始数据
            [__weakSelf processDataAsyncAndUpdate:serverData range:range isRangeData:isRangeData];
        }
    }];
}

// 异步处理数据并更新界面
- (void)processDataAsyncAndUpdate:(NSArray *)serverData range:(NSRange)range isRangeData:(BOOL)isRangeData {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 在后台线程进行数据排序等处理
        NSArray *sortedData = [self sortDataArray:serverData isRangeData:isRangeData];

        dispatch_async(dispatch_get_main_queue(), ^{
            // 回到主线程更新UI
            [self updateDataAtRange:range withData:sortedData isRangeData:isRangeData];
        });
    });
}

#pragma mark - Private
- (void)showGuideViewIfNeeded {
    // 检查是否需要显示
    if (![FMNewGuideViewController shouldShowGuideForVCClassName:NSStringFromClass(self.class)]) {
        return;
    }
    
    UIView *jumpBtn = (UIView *)self.navigationItem.rightBarButtonItem.customView;
    UIButton *leftSlider = self.dateDragView.leftSlider;
    UIButton *rightSlider = self.dateDragView.rightSlider;
    self.dateDragView.redContainerView.hidden = NO;

    if (!jumpBtn || !leftSlider || !rightSlider) {
        return;
    }

    CGRect jumpBtnRect = [jumpBtn.superview convertRect:jumpBtn.frame toView:nil];
    CGRect leftSliderRect = [leftSlider.superview convertRect:leftSlider.frame toView:nil];
    CGRect rightSliderRect = [rightSlider.superview convertRect:rightSlider.frame toView:nil];
    
    // 调整leftSliderRect和rightSliderRect使其包含整个滑块视觉区域
    jumpBtnRect = CGRectInset(jumpBtnRect, -5, -5);
    leftSliderRect = CGRectInset(leftSliderRect, -5, -5);
    rightSliderRect = CGRectInset(rightSliderRect, -5, -5);

    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:@"点击跳转超级龙虎榜，查看龙虎榜详细信息"];
    attrStr.yy_font = FontWithSize(18);
    attrStr.yy_color = FMZeroColor;
    [attrStr yy_setColor:FMNavColor range:NSMakeRange(4, 5)];
    CGSize strSize = [attrStr boundingRectWithSize:CGSizeMake(275, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin context:nil].size;
    NSDictionary *jumpBtnItem = @{
        @"rect": [NSValue valueWithCGRect:jumpBtnRect],
        @"attrText": attrStr,
        @"tipViewFrame": [NSValue valueWithCGRect:CGRectMake(UI_SCREEN_WIDTH - 275 - 30 - 10, CGRectGetMaxY(jumpBtnRect) + 10, 275 + 30, ceil(strSize.height) + 37.5)]
    };

    NSDictionary *leftSliderItem = @{
        @"rect": [NSValue valueWithCGRect:leftSliderRect],
        @"attrText": @"",
    };

    NSMutableAttributedString *attrStr2 = [[NSMutableAttributedString alloc] initWithString:@"滑动滑块，修改统计时段"];
    attrStr2.yy_font = FontWithSize(18);
    attrStr2.yy_color = FMZeroColor;
    CGSize strSize2 = [attrStr2 boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin context:nil].size;
    CGSize tipViewSize2 = CGSizeMake(ceil(strSize2.width) + 30, ceil(strSize2.height + 37.5)); // 37.5是文本上下内边距各15+7.5的三角形高度
    CGFloat tipViewRight2 = MIN(CGRectGetMidX(rightSliderRect) + 25, UI_SCREEN_WIDTH); // 25是大概给的
    CGFloat tipViewLeft2 = MAX(0, tipViewRight2 - tipViewSize2.width);
    NSDictionary *rightSliderItem = @{
        @"rect": [NSValue valueWithCGRect:rightSliderRect],
        @"attrText": attrStr2,
        @"tipViewFrame": [NSValue valueWithCGRect:CGRectMake(tipViewLeft2, CGRectGetMinY(rightSliderRect) - 10 - ceil(strSize2.height) - 37.5, tipViewSize2.width, tipViewSize2.height)]
    };
    
    
    FMNewGuideViewController *newGuideVC = [[FMNewGuideViewController alloc] initWithTargetVCClassName:NSStringFromClass(self.class)];
    newGuideVC.guideItems = @[jumpBtnItem, leftSliderItem, rightSliderItem];
    [newGuideVC show];
    newGuideVC.guideCompletion = ^{
        self.dateDragView.redContainerView.hidden = YES;
    };
}

// 生成占位符数据
- (void)generatePlaceholderDataWithCount:(NSInteger)count isRangeData:(BOOL)isRangeData {
    NSMutableArray *placeholderArray = [NSMutableArray array];
    
    for (NSInteger i = 0; i < count; i++) {
        if (isRangeData) {
            FMRealTimeDragonTigerStockRangeModel *placeholderModel = [[FMRealTimeDragonTigerStockRangeModel alloc] init];
            placeholderModel.stockName = @"--";
            placeholderModel.stockCode = [NSString stringWithFormat:@"placeholder_%ld", (long)i];
            [placeholderArray addObject:placeholderModel];
        } else {
            FMRealTimeDragonTigerStockInfoModel *placeholderModel = [[FMRealTimeDragonTigerStockInfoModel alloc] init];
            placeholderModel.stockName = @"--";
            placeholderModel.stockCode = [NSString stringWithFormat:@"placeholder_%ld", (long)i];
            [placeholderArray addObject:placeholderModel];
        }
    }
    
    if (isRangeData) {
        self.rangePools = [placeholderArray copy];
        self.pools = nil;
    } else {
        self.pools = [placeholderArray copy];
        self.rangePools = nil;
    }
}

// 更新指定范围的数据
- (void)updateDataAtRange:(NSRange)range withData:(NSArray *)newData isRangeData:(BOOL)isRangeData {
    // 检查范围是否越界
    if (range.location + range.length > (isRangeData ? self.rangePools.count : self.pools.count)) {
        return;
    }
    if (range.length != newData.count) {
        return;
    }

    if (isRangeData) {
        NSMutableArray *mutableRangePools = [self.rangePools mutableCopy];
        [mutableRangePools replaceObjectsInRange:range withObjectsFromArray:newData];
        self.rangePools = [mutableRangePools copy];
    } else {
        NSMutableArray *mutablePools = [self.pools mutableCopy];
        [mutablePools replaceObjectsInRange:range withObjectsFromArray:newData];
        self.pools = [mutablePools copy];
    }

    if (self.needScrollTop) {
        [self.stockCell setContentOffset:CGPointZero];
    }

    // 调用防抖方法更新UI
    [self reloadTableViewData];
}

// 防抖刷新方法
- (void)reloadTableViewData {
    [self.tableView reloadData];
}

// 处理Cell的滚动停止回调
- (void)handleScrollStoppedWithStartIndex:(NSInteger)startIndex endIndex:(NSInteger)endIndex {
    self.beginIndex = startIndex;
    self.endIndex = endIndex;
    self.needScrollTop = NO;
    
//    NSLog(@"滚动停止，请求数据范围: %ld-%ld", (long)startIndex, (long)endIndex);
    if (!self.startTime) {
        [self requestData];
    } else {
        [self requestRangeData];
    }
}

// 将服务器返回股票数据转换成向优品请求的股票结构
- (NSArray<UPHqStockUnique *> *)getReqRangeArrayFromData:(NSArray *)dataArray {
    NSMutableArray<UPHqStockUnique *> *stockArray = [NSMutableArray array];

    for (id model in dataArray) {
        NSString *stockCode = [model stockCode];

        // 过滤掉占位符数据和空数据
        if (stockCode.length == 0 || [stockCode hasPrefix:@"placeholder_"]) {
            continue;
        }

        UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:stockCode];
        if (info) {
            UPHqStockUnique *stock = [UPHqStockUnique new];
            stock.setCode = info.setCode;
            stock.code = info.code;
            [stockArray addObject:stock];
        }
    }

    return [stockArray copy];
}

// 用UP数据更新服务器返回的数据模型
- (NSArray *)updateServerData:(NSArray *)serverData withUPData:(NSArray<UPHqStockHq *> *)upDataArray isRangeData:(BOOL)isRangeData {
    if (upDataArray.count == 0) {
        return serverData;
    }

    NSMutableArray *updatedData = [serverData mutableCopy];

    for (NSInteger i = 0; i < updatedData.count; i++) {
        id model = updatedData[i];
        NSString *stockCode = [model stockCode];

        // 过滤掉占位符数据和空数据
        if (stockCode.length == 0 || [stockCode hasPrefix:@"placeholder_"]) {
            continue;
        }

        // 查找对应的UP数据
        for (UPHqStockHq *upData in upDataArray) {
            NSString *upStockCode = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:upData.setCode code:upData.code]];

            if ([stockCode.lowercaseString isEqualToString:upStockCode.lowercaseString]) {
                // 更新字段
                [self updateModel:model withUPData:upData isRangeData:isRangeData];
                break;
            }
        }
    }

    // 对更新后的数据进行排序
    NSArray *sortedData = [self sortDataArray:updatedData isRangeData:isRangeData];

    return sortedData;
}

// 使用UP数据更新模型字段
- (void)updateModel:(id)model withUPData:(UPHqStockHq *)upData isRangeData:(BOOL)isRangeData {
    if (isRangeData) {
        // 区间统计模型更新
        FMRealTimeDragonTigerStockRangeModel *rangeModel = (FMRealTimeDragonTigerStockRangeModel *)model;

        // 最新价
        rangeModel.lastPx = upData.nowPrice;
        // 涨跌幅
        rangeModel.pxChangeRate = upData.changeRatio * 100.0;
        // 总市值
        rangeModel.nowMarketValue = upData.totalMarketValue;
        // 流通市值
        rangeModel.nowCirculationValue = upData.circulationMarketValue;
        // 量比
        rangeModel.volRatio = upData.volRatio;
        // 换手率
        rangeModel.turnoverRatio = upData.turnoverRate * 100.0;
    } else {
        // 实时列表模型更新
        FMRealTimeDragonTigerStockInfoModel *infoModel = (FMRealTimeDragonTigerStockInfoModel *)model;

        // 最新价
        infoModel.lastPx = upData.nowPrice;
        // 涨跌幅
        infoModel.pxChangeRate = upData.changeRatio * 100.0;
        // 成交额
        infoModel.businessBalance = upData.dealAmount;
        // 总市值
        infoModel.marketValue = upData.totalMarketValue;
        // 流通市值
        infoModel.circulationValue = upData.circulationMarketValue;
        // 量比
        infoModel.volRatio = upData.volRatio;
        // 换手率
        infoModel.turnoverRatio = upData.turnoverRate * 100.0;
        // 涨速
        infoModel.dUpSpeed = upData.upSpeed * 100.0;
    }
}



// 重新排序
- (NSArray *)sortDataArray:(NSArray *)dataArray isRangeData:(BOOL)isRangeData {
    if (dataArray.count == 0) {
        return dataArray;
    }

    NSString *sortKey = [RealTimeDragonSortTool sortKeyForOrderParam:self.orderParam isRangeData:isRangeData];
    return [FMHelper sortedDatas:dataArray withKey:sortKey ascending:!self.orderType];
}

- (void)closeBtnClick {
    [self.dateDragView resetUI];
    [self updateCloseBtnVisibility:YES]; // 隐藏关闭按钮
    self.startTime = nil;
    self.endTime = nil;
    self.orderType = YES;
    self.orderParam = 2; // 回到实时列表时默认为主力净额排序
   
    [self requestFromZero];
}

- (void)jumpToLHB {
    [ProtocolJump jumpWithUrl:@"qcyzt://lhrank"];
}

- (void)updateDragViewDate {
    NSDate *selectedDate = [NSDate dateFromFormatedString:self.chooseDate format:@"yyyy-MM-dd"];
    if ([selectedDate isSameDayWithDate:[NSDate date]]) { // 如果不是当天，传当前时间
        [self.dateDragView updateCurrentDate:[NSDate date]];
    } else {
        [self.dateDragView updateCurrentDate:selectedDate];
    }
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.bounces = NO;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        
        _tableView.tableHeaderView = self.indexView;
    }
    
    return _tableView;
}

- (FMRealtimeDragonTigerIndexView *)indexView {
    if (!_indexView) {
        _indexView = [[FMRealtimeDragonTigerIndexView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 108)];
    }
    return _indexView;
}

- (FMRealtimeDragonTigerControlView *)controlView {
    if (!_controlView) {
        _controlView = [[FMRealtimeDragonTigerControlView alloc] init];
        WEAKSELF
        _controlView.dateSelectedBlock = ^(NSDate * _Nonnull selectedDate) {
            __weakSelf.chooseDate = [selectedDate dateStringWithFormatString:@"yyyy-MM-dd"];

            dispatch_async(dispatch_get_main_queue(), ^{
                [__weakSelf showGuideViewIfNeeded];
            });

            // 判断当前是否处于筛选状态
            if (__weakSelf.startTime && __weakSelf.startTime.length > 0) {
                // 如果当前是筛选状态，调用closeBtnClick重置状态
                [__weakSelf closeBtnClick];
            } else {
                // 如果当前不是筛选状态，只请求数据，保持排序状态
                [__weakSelf requestFromZero];
            }
        };
        
        _controlView.tabSwitchBlock = ^(NSInteger selectedIndex) {
            __weakSelf.isSelfStock = selectedIndex == 1;
            if (__weakSelf.isSelfStock) {
                NSArray *selfStocks = [FMSelfOptionStockCacheTool getSelfStocks];
                if (selfStocks.count == 0) {
                    __weakSelf.pools = @[];
                    __weakSelf.rangePools = @[];
                    [__weakSelf.tableView reloadData];
                    return;
                }
            }
            [__weakSelf requestFromZero];
        };
        
        _controlView.filterChangedBlock = ^(NSArray<NSNumber *> * _Nonnull selectedIndices) {
            [selectedIndices enumerateObjectsUsingBlock:^(NSNumber * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if (idx == 0) {
                    __weakSelf.isKC = obj.boolValue;
                } else if (idx == 1) {
                    __weakSelf.isST = obj.boolValue;
                }
                
                [__weakSelf requestFromZero];
            }];
        };

    }
    return _controlView;
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] init];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.alignment = UIStackViewAlignmentFill;
        _stackView.spacing = 0;
    }
    return _stackView;
}

- (UIView *)closeBtnView {
    if (!_closeBtnView) {
        _closeBtnView = [[UIView alloc] init];
        _closeBtnView.backgroundColor = UIColor.clearColor;
        
        UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [closeBtn setTitle:@"关闭统计" forState:UIControlStateNormal];
        [closeBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
        [closeBtn setBackgroundColor:ColorWithHex(0x2A8AFB)];
        closeBtn.titleLabel.font = FontWithSize(12);
        [closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [_closeBtnView addSubview:closeBtn];
        [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(54);
            make.top.bottom.equalTo(0);
            make.right.equalTo(-10);
        }];
    }
    return _closeBtnView;
}

- (FMRealtimeDragonTigerDragView *)dateDragView {
    if (!_dateDragView) {
        _dateDragView = [[FMRealtimeDragonTigerDragView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 60) currentDate:[NSDate date]];

        // 设置拖拽回调
        WEAKSELF
        _dateDragView.dragViewBlock = ^(BOOL statusChanged, BOOL isGestureRecognizerStateEnd, NSString * _Nonnull leftTimeStr, NSString * _Nonnull rightTimeStr) {
            if (!isGestureRecognizerStateEnd) {
                [__weakSelf updateCloseBtnVisibility:NO]; // 显示关闭按钮
            } else {
                __weakSelf.startTime = leftTimeStr;
                __weakSelf.endTime = rightTimeStr;
                
                if (statusChanged) { // 非筛选变成筛选，修改排序状态
                    __weakSelf.orderType = YES;
                    __weakSelf.orderParam = 1; // 筛选时默认为区间净额排序
                }
                [__weakSelf requestFromZero];
            }
        };
    }
    return _dateDragView;
}

- (FMRealTimeDragonTigerStockInfoCell *)stockCell {
    if (!_stockCell) {
        _stockCell = [[FMRealTimeDragonTigerStockInfoCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@""];
        
        WEAKSELF
        _stockCell.sortChangedBlock = ^(BOOL orderType, NSInteger orderParam) {
            __weakSelf.orderType = orderType;
            __weakSelf.orderParam = orderParam;
            
            [__weakSelf requestFromZero];
        };
        
        // 添加滚动停止回调
        _stockCell.scrollStoppedBlock = ^(NSInteger startIndex, NSInteger endIndex) {
            [__weakSelf handleScrollStoppedWithStartIndex:startIndex endIndex:endIndex];
        };
        
        _stockCell.pageChangedBlock = ^(NSInteger currentPage) {
            if (currentPage == 0) {
                __weakSelf.dateDragView.hidden = YES;
                [__weakSelf updateCloseBtnVisibility:YES]; // 隐藏关闭按钮
            } else {
                __weakSelf.dateDragView.hidden = NO;
                // 根据是否有区间统计来决定是否显示关闭按钮
                BOOL shouldHideCloseBtn = (__weakSelf.startTime == nil || __weakSelf.startTime.length == 0);
                [__weakSelf updateCloseBtnVisibility:shouldHideCloseBtn];
            }
            [__weakSelf.tableView reloadData];
        };
    }
    
    return _stockCell;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [UPMarketMonitor monitorWithInterval:5 ignoreMarketStatus:NO];
    }
    
    return _monitor;
}

// 添加一个方法统一管理关闭按钮的显示状态
- (void)updateCloseBtnVisibility:(BOOL)hidden {
    self.closeBtnView.hidden = self.closeBtnViewHidden = hidden;
}

//- (void)setOrderParam:(NSInteger)orderParam {
//    _orderParam = orderParam;
//    
//    NSLog(@"orderParm -- %zd", orderParam);
//}

@end
