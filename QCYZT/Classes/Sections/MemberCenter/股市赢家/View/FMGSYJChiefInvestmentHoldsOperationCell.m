//
//  FMGSYJChiefInvestmentHoldsOperationCell.m
//  QCYZT
//
//  Created by Augment on 2024/6/28.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMGSYJChiefInvestmentHoldsOperationCell.h"
#import "HTMLParseTool.h"
#import "FMGSYJPDFItemView.h"
#import "HZPhotoBrowser.h"
#import <CoreText/CoreText.h>

@interface FMGSYJChiefInvestmentHoldsOperationCell() <HZPhotoBrowserDelegate>

@property (nonatomic, strong) UIImageView *tagImageV;
@property (nonatomic, strong) UIView *leftLineView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *dateTimeLabel;
@property (nonatomic, strong) YYLabel *positionLabel;
@property (nonatomic, strong) UILabel *priceLabel;
@property (nonatomic, strong) UIView *descBgView;
@property (nonatomic, strong) YYLabel *descLabel;
@property (nonatomic, strong) YYLabel *reminderLabel;
@property (nonatomic, strong) UIStackView *mainStackView;
@property (nonatomic, strong) UIStackView *pdfStackView;
@property (nonatomic, strong) NSArray *imageUrls; // 存储当前内容中的所有图片URL

@end

@implementation FMGSYJChiefInvestmentHoldsOperationCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    // 标签图片
    self.tagImageV = [[UIImageView alloc] init];
    [self.contentView addSubview:self.tagImageV];

    // 左侧竖线
    self.leftLineView = [[UIView alloc] init];
    self.leftLineView.backgroundColor = UIColor.fm_sepline_color;
    [self.contentView addSubview:self.leftLineView];
    
    // 主StackView
    self.mainStackView = [[UIStackView alloc] initWithArrangedSubviews:@[]];
    self.mainStackView.axis = UILayoutConstraintAxisVertical;
    self.mainStackView.alignment = UIStackViewAlignmentFill;
    self.mainStackView.distribution = UIStackViewDistributionFill;
    self.mainStackView.spacing = 10;
    [self.contentView addSubview:self.mainStackView];
    [self.mainStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(10, 37, 10, 15));
    }];
    
    // 顶部信息视图
    UIView *topView = [[UIView alloc] init];
    [self.mainStackView addArrangedSubview:topView];
    
    // 标题（如"建议减仓"）
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = BoldFontWithSize(14);
    self.titleLabel.textColor = UIColor.up_textPrimaryColor;
    [topView addSubview:self.titleLabel];
    
    // 日期时间
    self.dateTimeLabel = [[UILabel alloc] init];
    self.dateTimeLabel.font = FontWithSize(12);
    self.dateTimeLabel.textColor = UIColor.up_textSecondary1Color;
    [topView addSubview:self.dateTimeLabel];
    
    // 中间信息视图
    UIView *middleView = [[UIView alloc] init];
    [self.mainStackView addArrangedSubview:middleView];
    
    // 仓位描述
    self.positionLabel = [[YYLabel alloc] init];
    [middleView addSubview:self.positionLabel];
    
    // 参考价格
    self.priceLabel = [[UILabel alloc] init];
    self.priceLabel.font = FontWithSize(14);
    self.priceLabel.textColor = UIColor.up_riseColor;
    self.priceLabel.textAlignment = NSTextAlignmentRight;
    [middleView addSubview:self.priceLabel];
    
    // 详细描述
    self.descBgView = [[UIView alloc] init];
    self.descBgView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    UI_View_Radius(self.descBgView, 4.0f);
    [self.mainStackView addArrangedSubview:self.descBgView];
    
    self.descLabel = [[YYLabel alloc] init];
    self.descLabel.font = FontWithSize(13);
    self.descLabel.numberOfLines = 0;
    [self.descBgView addSubview:self.descLabel];
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(10);
        make.right.equalTo(-10);
    }];
    
    [self setupImageTapAction];
    
    // PDF文件堆栈视图
    self.pdfStackView = [[UIStackView alloc] initWithArrangedSubviews:@[]];
    self.pdfStackView.axis = UILayoutConstraintAxisVertical;
    self.pdfStackView.alignment = UIStackViewAlignmentFill;
    self.pdfStackView.distribution = UIStackViewDistributionFill;
    self.pdfStackView.spacing = 0;
    [self.descBgView addSubview:self.pdfStackView];
    [self.pdfStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(0);
        make.top.equalTo(self.descLabel.mas_bottom).offset(10);
    }];
    
    // 提示文本
    self.reminderLabel = [YYLabel new];
    self.reminderLabel.numberOfLines = 0;
    [self.mainStackView addArrangedSubview:self.reminderLabel];
    
    // 布局
    [self.tagImageV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.centerY.equalTo(self.titleLabel);
        make.width.height.equalTo(17);
    }];
    
    [self.leftLineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.tagImageV);
        make.bottom.equalTo(0);
        make.top.equalTo(self.tagImageV.mas_bottom);
        make.width.equalTo(0.7);
    }];
    
    [topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(20);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(0);
        make.centerY.equalTo(0);
    }];
    
    [self.dateTimeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(20);
        make.centerY.equalTo(0);
    }];
    
    [middleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(20);
    }];
    
    [self.positionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(0);
        make.centerY.equalTo(0);
    }];
    
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(0);
        make.centerY.equalTo(0);
    }];
}

- (void)setModel:(FMGSYJChiefInvestmentOperationModel *)model {
    _model = model;
    
    NSString *imgStr;
    NSString *reminderStr;
    NSString *operatorStr;
    if (model.operaType == 0) {
        imgStr = @"gsyj_buy";
        reminderStr = @"温馨提示：投资有风险，入市需谨慎！操作建议仅供当日参考，如有操作，严格控制仓位，自负盈亏。";
        operatorStr = @"建议关注";
    } else if (model.operaType == 1) {
        imgStr = @"gsyj_add";
        reminderStr = @"温馨提示：投资有风险，入市需谨慎！操作建议仅供当日参考，如有操作，严格控制仓位，自负盈亏。";
        operatorStr = @"建议补仓";
    } else if (model.operaType == 2) {
        imgStr = @"gsyj_follow";
        reminderStr = @"温馨提示：投资有风险，入市需谨慎！以上内容，仅供跟踪期内参考。";
        operatorStr = @"建议跟踪";
    } else if (model.operaType == 3) {
        imgStr = @"gsyj_reduce";
        reminderStr = @"温馨提示：投资有风险，入市需谨慎！操作建议仅供当日参考，如有操作，严格控制仓位，自负盈亏。";
        operatorStr = @"建议减仓";
    } else if (model.operaType == 4) {
        imgStr = @"gsyj_sell";
        reminderStr = @"温馨提示：投资有风险，入市需谨慎！操作建议仅供参考，如全仓卖出，则后期不再跟踪。";
        operatorStr = @"建议取关";
    }
    self.tagImageV.image = ImageWithName(imgStr);
    self.titleLabel.text = operatorStr;
    
    self.dateTimeLabel.text = model.operaTimeStr;
    
    NSString *positionDesc = [NSString stringWithFormat:@"仓位 %zd%% ", model.holdScale];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:positionDesc];
    UIImage *arrowImage = ImageWithName(@"gsyj_arrow");
    NSAttributedString *arrowStr = [NSAttributedString yy_attachmentStringWithContent:arrowImage contentMode:UIViewContentModeCenter attachmentSize:arrowImage.size alignToFont:FontWithSize(12) alignment:YYTextVerticalAlignmentCenter];
    [attrStr appendAttributedString:arrowStr];
    [attrStr appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@" %zd%%", model.curHoldScale]]];
    [attrStr addAttributes:@{NSFontAttributeName : FontWithSize(12), NSForegroundColorAttributeName : UIColor.up_textSecondaryColor} range:NSMakeRange(0, 2)];
    [attrStr addAttributes:@{NSFontAttributeName : FontWithSize(14), NSForegroundColorAttributeName : UIColor.up_textPrimaryColor} range:NSMakeRange(2, attrStr.length - 2)];
    self.positionLabel.attributedText = attrStr;
    
    // 设置参考价格
    NSString *priceDesc;
    if (model.consultPrice.length > 0) {
        priceDesc = [NSString stringWithFormat:@"参考价 %.2f", model.consultPrice.floatValue];
    } else {
        priceDesc = @"参考价 -";
    }
    self.priceLabel.attributedText = [priceDesc attrStrWithMatchColor:UIColor.up_textSecondaryColor pattern:@"参考价" textFont:FontWithSize(12)];
    
    // 更新PDF视图
    if (model.pdfList.count) {
        [self setupPDFViews];
        self.pdfStackView.hidden = NO;
    } else {
        for (UIView *view in self.pdfStackView.arrangedSubviews) {
            [view removeFromSuperview];
        }
        self.pdfStackView.hidden = YES;
    }
    
    self.descLabel.preferredMaxLayoutWidth = self.cellWidth - 72;
    if (model.cachedAttributedString) {
        self.descLabel.attributedText = model.cachedAttributedString;
        self.descLabel.preferredMaxLayoutWidth = self.cellWidth - 72; // 设置缓存富文本后立即设置宽度约束
        self.descBgView.hidden = NO;
        // 从缓存的富文本中提取图片URL
        [self extractImageUrlsFromAttributedString:model.cachedAttributedString];
    } else {
//        self.descBgView.hidden = YES; // 先隐藏，等解析完成再显示
        model.cachedAttributedString = attrStr;
        [HTMLParseTool shareInstance].textColor = UIColor.up_textPrimaryColor;
        [HTMLParseTool shareInstance].textFontSize = 13.0f;
        [HTMLParseTool shareInstance].lineSpacing = 3.0f;
        [HTMLParseTool shareInstance].paragraphSpacing = 5.0f;
        [HTMLParseTool shareInstance].alignment = NSTextAlignmentLeft;
        [HTMLParseTool shareInstance].imgHeadIndent = NO;
        [HTMLParseTool shareInstance].imageWidth = self.cellWidth - 72;
        [HTMLParseTool shareInstance].useYYLabelCompatibleMode = YES; // 启用YYLabel兼容模式
        [[HTMLParseTool shareInstance] parseWithHTMLString:model.investProposal contentId:model.operationId completeWithAttrStr:^(NSMutableAttributedString *attrStr, NSArray *imgs, BOOL loadCompleted) {
                            if (loadCompleted) {
                    self.descLabel.attributedText = attrStr;
                    self.descLabel.preferredMaxLayoutWidth = self.cellWidth - 72; // 设置富文本后立即设置宽度约束
                    self.descBgView.hidden = NO;
                    
                    // 存储图片URL数组，用于图片浏览器
                    self.imageUrls = imgs;
                    
                    // 缓存解析结果
                    model.cachedAttributedString = attrStr;
                    
                    // 布局刷新，确保高度计算准确
                    [self setNeedsLayout];
                    [self layoutIfNeeded];
                    
                    if (self.refreshBlock) {
                        self.refreshBlock(model);
                    }
                }
        }];
    }
    
    self.reminderLabel.preferredMaxLayoutWidth = self.cellWidth - 52;
    NSMutableString *reminderString;
    if (self.dakas.count) {
        reminderString = [[NSMutableString alloc] initWithFormat:@"%@四川大决策投资顾问：", reminderStr];
        [self.dakas enumerateObjectsUsingBlock:^(FMChiefInvestmentSignDakaModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            [reminderString appendFormat:@"%@，证书编号：%@；", obj.name, obj.certCode];
        }];
    } else {
        reminderString = [[NSMutableString alloc] initWithString:reminderStr];
    }
    NSMutableAttributedString *attrStr3 = [[NSMutableAttributedString alloc] initWithString:reminderString];
    attrStr3.yy_font = FontWithSize(12);
    attrStr3.yy_color = UIColor.up_textSecondary2Color;
    attrStr3.yy_lineSpacing = 3.0;
    self.reminderLabel.attributedText = attrStr3;
    self.reminderLabel.hidden = NO;
}


- (void)setupPDFViews {
    // 清除之前的PDF视图
    for (UIView *view in self.pdfStackView.arrangedSubviews) {
        [view removeFromSuperview];
    }
    
    // 添加分隔线
    UIView *separatorLine = [[UIView alloc] init];
    separatorLine.backgroundColor = UIColor.fm_sepline_color;
    [self.pdfStackView addArrangedSubview:separatorLine];
    [separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(0.5);
    }];
        
    // 添加PDF视图
    for (NSInteger i = 0; i < self.model.pdfList.count; i++) {
        FMChiefInvestmentOperationPDFModel *pdfModel = self.model.pdfList[i];
        FMGSYJPDFItemView *pdfView = [FMGSYJPDFItemView pdfItemViewWithModel:pdfModel];
        [self.pdfStackView addArrangedSubview:pdfView];
        [pdfView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(44);
        }];
    }
}

#pragma mark - Public Methods

- (CGFloat)calculatedHeight {
    // 强制刷新布局，确保所有子视图尺寸正确
    [self layoutIfNeeded];
    
    // 计算stackView的总高度
    CGFloat totalHeight = 0;
    // 添加顶部视图高度 (固定20 + 上下内边距10)
    totalHeight += 40;
    // 添加中间视图高度 (固定20 + 上间距10)
    totalHeight += 30;
    
    // 如果有描述内容，计算描述视图高度（包含图片）
    if (self.descLabel.attributedText) {
        CGFloat descTextHeight = [self calculateYYLabelHeightWithAttributedText:self.descLabel.attributedText width:self.cellWidth - 72];
        
        // 加上内边距
        totalHeight += descTextHeight + 20; // 描述文本高度（包含图片） + 内边距
        totalHeight += 10; // 上间距
        
        FMLog(@"描述文本高度（包含图片）: %f", descTextHeight);
    } else {
        FMLog(@"没有描述文本");
    }
    
    // 如果有pdf，添加PDF分隔线高度
    if (self.pdfStackView.hidden == NO && self.model.pdfList.count > 0) {
        totalHeight += 0.5; // 分隔线高度
        totalHeight += 44 * self.model.pdfList.count; // PDF视图高度
    }
    
    // 如果有提示文本，计算提示文本高度
    if (self.reminderLabel.attributedText) {
        CGFloat reminderHeight = [self calculateYYLabelHeightWithAttributedText:self.reminderLabel.attributedText width:self.cellWidth - 52];
        
        totalHeight += reminderHeight;
        totalHeight += 10; // 上间距
        
        FMLog(@"提示文本高度: %f", reminderHeight);
    } else {
        FMLog(@"没有提示文本");
    }
    
    FMLog(@"Cell总高度: %f", totalHeight);
    return totalHeight;
}

#pragma mark - 高度计算辅助方法

- (CGFloat)calculateYYLabelHeightWithAttributedText:(NSAttributedString *)attributedText width:(CGFloat)width {
    if (!attributedText || attributedText.length == 0) {
        return 0;
    }
    
    // 方案1：使用YYTextLayout计算（最准确）
    YYTextLayout *layout = [YYTextLayout layoutWithContainerSize:CGSizeMake(width, CGFLOAT_MAX) text:attributedText];
    if (layout) {
        CGFloat height = ceil(layout.textBoundingSize.height);
        FMLog(@"YYTextLayout计算高度: %f", height);
        return height;
    }
    
    // 方案2：降级方案 - 使用CTFramesetter计算（兼容模式）
    CTFramesetterRef frameSetter = CTFramesetterCreateWithAttributedString((CFAttributedStringRef)attributedText);
    if (frameSetter) {
        CGSize suggestedSize = CTFramesetterSuggestFrameSizeWithConstraints(frameSetter, CFRangeMake(0, 0), NULL, CGSizeMake(width, CGFLOAT_MAX), NULL);
        CFRelease(frameSetter);
        
        CGFloat height = ceil(suggestedSize.height);
        FMLog(@"CTFramesetter计算高度: %f", height);
        return height;
    }
    
    // 方案3：最后降级方案 - 基础计算
    CGRect boundingRect = [attributedText boundingRectWithSize:CGSizeMake(width, CGFLOAT_MAX) 
                                                       options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading 
                                                       context:nil];
    CGFloat height = ceil(boundingRect.size.height);
    FMLog(@"boundingRect计算高度: %f", height);
    return height;
}

#pragma mark - 图片点击功能

- (void)setupImageTapAction {
    WEAKSELF
    self.descLabel.textTapAction = ^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        if (range.location == NSNotFound) {
            return;
        }
        // 检查点击的是否是图片链接
        NSDictionary *attributes = [text attributesAtIndex:range.location effectiveRange:NULL];
        NSString *link = attributes[NSLinkAttributeName];
        
        if (link && [link hasPrefix:@"img:"]) {
            // 提取图片URL
            NSString *imageUrl = [link substringFromIndex:4]; // 去掉"img:"前缀
            [__weakSelf showImageBrowserWithImageUrl:imageUrl];
        }
    };
}

- (void)extractImageUrlsFromAttributedString:(NSAttributedString *)attributedString {
    NSMutableArray *urls = [NSMutableArray array];
    
    [attributedString enumerateAttribute:NSLinkAttributeName inRange:NSMakeRange(0, attributedString.length) options:0 usingBlock:^(id value, NSRange range, BOOL *stop) {
        if (value && [value isKindOfClass:[NSString class]]) {
            NSString *link = (NSString *)value;
            if ([link hasPrefix:@"img:"]) {
                NSString *imageUrl = [link substringFromIndex:4]; // 去掉"img:"前缀
                if (imageUrl.length > 0) {
                    [urls addObject:imageUrl];
                }
            }
        }
    }];
    
    self.imageUrls = [urls copy];
}

- (void)showImageBrowserWithImageUrl:(NSString *)imageUrl {
    if (!self.imageUrls.count) {
        return;
    }
    
    // 找到点击图片在数组中的索引
    NSInteger currentIndex = 0;
    for (NSInteger i = 0; i < self.imageUrls.count; i++) {
        if ([self.imageUrls[i] isEqualToString:imageUrl]) {
            currentIndex = i;
            break;
        }
    }
    
    // 创建并显示图片浏览器
    HZPhotoBrowser *photoBrowser = [[HZPhotoBrowser alloc] init];
    photoBrowser.imageArray = self.imageUrls;
    photoBrowser.currentImageIndex = (int)currentIndex;
    photoBrowser.delegate = self;
    [photoBrowser show];
}

#pragma mark - HZPhotoBrowserDelegate

- (UIImage *)photoBrowser:(HZPhotoBrowser *)browser placeholderImageForIndex:(NSInteger)index {
    // 返回占位图片
    return ImageWithName(@"note_placeholder");
}

- (NSURL *)photoBrowser:(HZPhotoBrowser *)browser highQualityImageURLForIndex:(NSInteger)index {
    if (index < self.imageUrls.count) {
        return [NSURL URLWithString:self.imageUrls[index]];
    }
    return nil;
}

@end
