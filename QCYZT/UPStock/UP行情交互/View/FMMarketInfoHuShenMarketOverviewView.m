//
//  FMMarketInfoHuShenMarketOverviewView.m
//  QCYZT
//
//  Created by zeng on 2024/11/26.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMMarketInfoHuShenMarketOverviewView.h"
#import "FMMarketInfoHuShenDpzjjlrGraphView.h"
#import "FMMarketInfoHuShenZdjsGraphView.h"
#import "FMMaketInfoHuShenMarketOverviewModel.h"

@interface HuShenMarketOverviewTopView : UIView

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UILabel *topLabel;
@property (nonatomic, strong) UILabel *bottomLabel;

@property (nonatomic, assign) BOOL choosed;

@end

@implementation HuShenMarketOverviewTopView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    
    if (self) {
        [self setup];
    }
    return self;
}

- (void)setup {
    UIView *bgView = [UIView new];
    [self addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    UI_View_BorderRadius(bgView, 5, 0.5, FMClearColor);
    bgView.backgroundColor = UIColor.fm_market_f10_tagView_label_bgColor;
    self.bgView = bgView;
    
    UILabel *topLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [bgView addSubview:topLabel];
    [topLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(10);
        make.height.equalTo(21);
    }];
    self.topLabel = topLabel;
    
    UILabel *bottomLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_equalColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [bgView addSubview:bottomLabel];
    [bottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(topLabel.mas_bottom).offset(2);
        make.height.equalTo(24);
    }];
    self.bottomLabel = bottomLabel;
}

- (void)setChoosed:(BOOL)choosed {
    _choosed = choosed;
    
    if (choosed) {
        self.bgView.backgroundColor = UIColor.fm_market_overview_choosed_color;
        UI_View_BorderRadius(self.bgView, 5, 0.5, ColorWithHex(0xff9f57));
    } else {
        self.bgView.backgroundColor = UIColor.fm_market_f10_tagView_label_bgColor;
        UI_View_BorderRadius(self.bgView, 5, 0.5, FMClearColor);
    }
}

@end

@interface FMMarketInfoHuShenMarketOverviewView()

@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) HuShenMarketOverviewTopView *leftTopView;
@property (nonatomic, strong) HuShenMarketOverviewTopView *rightTopView;
@property (nonatomic, strong) FMMarketInfoHuShenDpzjjlrGraphView *netInflowGraphView;
@property (nonatomic, strong) FMMarketInfoHuShenZdjsGraphView *upDownGraphView;

@property (nonatomic, assign) NSInteger choosedIndex;
@property (nonatomic, strong) FMMaketInfoHuShenMarketOverviewModel *model;
@property (nonatomic, strong) UPMarketMonitor *monitor;

@end

@implementation FMMarketInfoHuShenMarketOverviewView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setup];
    }
    return self;
}

- (void)setup {
    self.backgroundColor = UIColor.up_contentBgColor;
    
    UIView *titleView = [UIView new];
    [self addSubview:titleView];
    [titleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.height.equalTo(50);
    }];
    
    UIView *sepline = [titleView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(15);
        make.centerY.equalTo(0);
        make.height.equalTo(12);
        make.width.equalTo(3);
    }];
    sepline.backgroundColor = UIColor.up_riseColor;
    UI_View_Radius(sepline, 1.5);
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [titleView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(sepline);
        make.left.equalTo(sepline.mas_right).offset(7.5);
    }];
    titleLabel.text = @"市场概况";
    
    UILabel *contentLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [titleView addSubview:contentLabel];
    [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(sepline);
    }];
    self.contentLabel = contentLabel;
    
    [self addSubview:self.leftTopView];
    [self.leftTopView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(titleView.mas_bottom);
        make.width.equalTo((UI_SCREEN_WIDTH - 40) * 0.5);
        make.height.equalTo(67);
    }];
    
    [self addSubview:self.rightTopView];
    [self.rightTopView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.top.equalTo(titleView.mas_bottom);
        make.width.height.equalTo(self.leftTopView);
    }];
    
    self.choosedIndex = 0;
    
    [self addSubview:self.netInflowGraphView];
    [self.netInflowGraphView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(self.leftTopView.mas_bottom);
        make.height.equalTo(194);
    }];
    
    [self insertSubview:self.upDownGraphView belowSubview:self.netInflowGraphView];
    [self.upDownGraphView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(self.leftTopView.mas_bottom);
        make.height.equalTo(self.netInflowGraphView);
    }];
}

- (void)viewDidAppear {
    [self requestData];
}

- (void)viewDidDisappear {
    [self.monitor stopMonitor];
}

- (void)requestData {
    UPMarketMinDataReq * req = [[UPMarketMinDataReq alloc] initWithSetCode:UPMarketSetCodeSH code:@"000001"];
    WeakSelf(weakSelf);
    [self.monitor startMonitorStockMinuteData:req tag:0 completionHandler:^(UPMarketMinDataRsp *rsp, NSError *error) {
        FMLog(@"%@想请求1条数据--", NSStringFromClass([self class]));
        if(!error && rsp.minuteDataArray.count > 0) {
            [HttpRequestTool requestStockListMarketOverviewWithStart:^{
            } failure:^{
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    weakSelf.model = [FMMaketInfoHuShenMarketOverviewModel modelWithDictionary:dic[@"data"]];
                    weakSelf.model.minuteItems = rsp.minuteDataArray.firstObject.minuteArray;
                    
                    NSString *str = [NSString stringWithFormat:@"总成交额 %@", weakSelf.model.businessBalance > 0 ? [FMHelper formattedStockValueWithNumber:weakSelf.model.businessBalance positiveSuffix:nil negativeSuffix:nil decimalPlacesForBillions:2 decimalPlacesForTenThousands:0] : @"--"];
                    weakSelf.contentLabel.attributedText = [str attrStrWithMatchColor:ColorWithHex(0xfc6c00) pattern:@"(\\d+(\\.\\d+)?)亿"
 textFont:BoldFontWithSize(12)];
                    weakSelf.leftTopView.bottomLabel.text = weakSelf.model.mainNetInflow != 0 ? [FMHelper formattedStockValueWithNumber:weakSelf.model.mainNetInflow positiveSuffix:nil negativeSuffix:@"-" decimalPlacesForBillions:2 decimalPlacesForTenThousands:0] : @"--";
                    weakSelf.leftTopView.bottomLabel.textColor = [UPMarketUICompareTool  compareWithData:weakSelf.model.mainNetInflow baseData:0 precise:0];
                    NSString *str2 = [NSString stringWithFormat:@"%zd:%zd:%zd", weakSelf.model.riseCount, weakSelf.model.flatCount, weakSelf.model.fallCount];
                    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:str2];
                    [attrStr addAttributeTextColor:UIColor.up_riseColor range:NSMakeRange(0, [NSString stringWithFormat:@"%zd", weakSelf.model.riseCount].length)];
                    [attrStr addAttributeTextColor:UIColor.up_fallColor range:NSMakeRange(str2.length - [NSString stringWithFormat:@"%zd", weakSelf.model.fallCount].length, [NSString stringWithFormat:@"%zd", weakSelf.model.fallCount].length)];
                    weakSelf.rightTopView.bottomLabel.attributedText = attrStr;

                    weakSelf.netInflowGraphView.model = weakSelf.model;
                    weakSelf.upDownGraphView.model = weakSelf.model;
                }
            }];
        }
    }];
}

- (void)changeChoose:(UIGestureRecognizer *)ges {
    HuShenMarketOverviewTopView *view = (HuShenMarketOverviewTopView *)ges.view;
    if (view == self.leftTopView) {
        if (self.choosedIndex == 0) {
            return;
        }
        
        self.choosedIndex = 0;
        self.leftTopView.choosed = YES;
        self.rightTopView.choosed = NO;
        self.netInflowGraphView.hidden = NO;
        self.upDownGraphView.hidden = YES;
    } else if (view == self.rightTopView) {
        if (self.choosedIndex == 1) {
            return;
        }
        
        self.choosedIndex = 1;
        self.rightTopView.choosed = YES;
        self.leftTopView.choosed = NO;
        self.netInflowGraphView.hidden = YES;
        self.upDownGraphView.hidden = NO;
    }
}

- (HuShenMarketOverviewTopView *)leftTopView {
    if (!_leftTopView) {
        _leftTopView = [HuShenMarketOverviewTopView new];
        _leftTopView.topLabel.text = @"大盘资金净流入";
        _leftTopView.choosed = YES;
        _leftTopView.bottomLabel.text = @"--";
        _leftTopView.userInteractionEnabled = YES;
        [_leftTopView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(changeChoose:)]];
    }
    
    return _leftTopView;
}

- (HuShenMarketOverviewTopView *)rightTopView {
    if (!_rightTopView) {
        _rightTopView = [HuShenMarketOverviewTopView new];
        _rightTopView.topLabel.text = @"涨跌家数";
        _rightTopView.bottomLabel.text = @"--";
        _rightTopView.userInteractionEnabled = YES;
        [_rightTopView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(changeChoose:)]];
    }
    
    return _rightTopView;
}

- (FMMarketInfoHuShenDpzjjlrGraphView *)netInflowGraphView {
    if (!_netInflowGraphView) {
        _netInflowGraphView = [FMMarketInfoHuShenDpzjjlrGraphView new];
    }
    
    return _netInflowGraphView;
}

- (FMMarketInfoHuShenZdjsGraphView *)upDownGraphView {
    if (!_upDownGraphView) {
        _upDownGraphView = [FMMarketInfoHuShenZdjsGraphView new];
    }
    
    return _upDownGraphView;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [UPMarketMonitor monitorWithInterval:6];
    }
    
    return _monitor;
}

@end
